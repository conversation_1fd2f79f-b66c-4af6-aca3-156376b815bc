﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using HerculesApi.Database;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using HerculesApi.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Runtime.ConstrainedExecution;
using Newtonsoft.Json;
using System.IO;
using Microsoft.AspNetCore.Authorization;
using System.Text.RegularExpressions;

namespace HerculesApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class GeraisController : ControllerBase
    {
        private readonly HerculesDbContext _context;
        public GeraisController(HerculesDbContext context)
        {
            _context = context;
        }
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Geral>>> GetGeral()
        {
            var gerais = _context.Gerais.AsNoTracking().Where(x => x.grl_vinculada == 0).ToList();
            foreach (var geral in gerais)
            {
                if (geral.grl_motivo != null || geral.grl_motivo != "")
                {
                    geral.grl_tipoServ = "CORREÇÃO";
                    geral.motivo = geral.grl_motivo;
                }
                if (geral.grl_nome == "CLIENTE PROVISÓRIO" && (geral.grl_motivo != null || geral.grl_motivo != ""))
                {
                    geral.grl_tipoServ = "CLIENTE PROVISÓRIO";
                    geral.motivo = geral.grl_nome;
                }
                if (geral.grl_status_uc == "AGUARDA RETIRADA DE MEDIDOR" && (geral.grl_motivo != null || geral.grl_motivo != ""))
                {
                    geral.grl_tipoServ = "RETIRADA DE MEDIDOR";
                    geral.motivo = geral.grl_status_uc;
                }
            }
            return gerais;
        }
        [HttpGet("count")]
        public async Task<ActionResult<int>> GetCount()
        {
            int qtd = await _context.Gerais.AsNoTracking().CountAsync();
            return Ok(qtd);
        }

        [HttpPost]
        public async Task<ActionResult<Geral>> PostGeral(List<Geral> gerais)
        {
            List<Adequacao> listaAdequacoes = new List<Adequacao>();
            List<Geral> geraisOk = new List<Geral>();
            var ger = _context.Gerais.AsNoTracking().ToList();

            if (ger.Count() > 0)
            {
                return BadRequest(new { message = "Já existe conteúdo na tabela Gerais!" });
            }
            var justificativas = _context.Justificativas.AsNoTracking().ToList();
            var ordensAbertas = _context.OrdensServico.AsNoTracking().Where(x => x.ord_status == 0).Select(x => x.ord_cliId).ToList();
            var ordensConcluidas = _context.OrdensServico.AsNoTracking().Where(x => x.ord_status == 1).Select(x => x.ord_cliId).ToList();
            var ade = _context.Adequacoes.AsNoTracking().Select(x => x.ade_cliId).ToList();
            var listaGerais = gerais.Where(x => !ade.Contains(Convert.ToInt32(x.grl_id))).ToList();
            var listaGeraisAdequacoes = gerais.Where(x => ade.Contains(Convert.ToInt32(x.grl_id))).ToList();
            foreach (Geral g in listaGeraisAdequacoes)
            {
                var a1 = _context.Adequacoes.AsNoTracking().Where(x => x.ade_cliId == g.grl_id).First();
                string aMotivo = a1.ade_motivo?.Substring(0, 4);
                if (g.grl_motivo != null)
                {
                    string gMotivo = g.grl_motivo?.Substring(0, 4);
                    bool contemJustificativa = justificativas.Any(j =>
                        j.jus_descricao != null &&
                        j.jus_descricao.Contains(gMotivo));
                    if (contemJustificativa)
                    {
                        if (!aMotivo.Equals("0.01"))
                        {
                            if (!aMotivo.Equals(gMotivo))
                            {
                                if (g.grl_motivo.Contains("-"))
                                {
                                    // Separa 'grl_motivo' em duas partes antes e depois do '-'
                                    var partes = g.grl_motivo.Split(new[] { '-' }, 2);
                                    a1.ade_motivo = partes[0].Trim(); // Pega a parte antes do '-'
                                    a1.ade_obs = partes[1].Trim();    // Pega a parte depois do '-'
                                }
                                else
                                {
                                    a1.ade_motivo = g.grl_motivo;
                                }
                                _context.Adequacoes.Update(a1);
                            }
                        }
                    }
                    else
                    {
                        if (!aMotivo.Equals("0.01"))
                        {
                            var a2 = _context.Adequacoes.AsNoTracking().Where(x => x.ade_cliId == g.grl_id).First();
                            _context.Adequacoes.Remove(a2);

                            g.grl_vinculada = 0;
                            geraisOk.Add(g);
                        }
                    }
                }
                else
                {
                    var a3 = _context.Adequacoes.AsNoTracking().Where(x => x.ade_cliId == g.grl_id).First();
                    _context.Adequacoes.Remove(a3);

                    geraisOk.Add(g);
                }
            }

            foreach (Geral geral in listaGerais)
            {
                if (!string.IsNullOrEmpty(geral.grl_motivo))
                {
                    string pMotivo = geral.grl_motivo?.Substring(0, 4);
                    bool contemJustificativa = justificativas.Any(j =>
                        j.jus_descricao != null &&
                        j.jus_descricao.Contains(pMotivo));
                    if (contemJustificativa)
                    {
                        Adequacao a = new Adequacao();
                        a.ade_cliId = Convert.ToInt32(geral.grl_id);
                        a.ade_cp = Convert.ToInt32(geral.grl_cp);
                        a.ade_data = Convert.ToString(DateOnly.FromDateTime(DateTime.Now));
                        a.ade_tecnicos = "Comissionamento";
                        a.ade_empreiteira = geral.grl_empreiteira;
                        a.ade_situacao = geral.grl_status_uc;
                        a.ade_obs = "";
                        if (geral.grl_motivo.Contains("-"))
                        {
                            // Separa 'grl_motivo' em duas partes antes e depois do '-'
                            var partes = geral.grl_motivo.Split(new[] { '-' }, 2);
                            geral.grl_motivo = partes[0].Trim(); // Pega a parte antes do '-'
                            geral.grl_obs = partes[1].Trim();    // Pega a parte depois do '-'
                        }
                        a.ade_motivo = geral.grl_motivo;
                        if (geral.grl_obs != null)
                        {
                            a.ade_obs = geral.grl_obs;
                        }
                        listaAdequacoes.Add(a);
                    }
                    else
                    {
                        if (geral.grl_motivo.Contains("-"))
                        {
                            // Separa 'grl_motivo' em duas partes antes e depois do '-'
                            var partes = geral.grl_motivo.Split(new[] { '-' }, 2);
                            geral.grl_motivo = partes[0].Trim(); // Pega a parte antes do '-'
                            geral.grl_obs = partes[1].Trim();    // Pega a parte depois do '-'
                            geral.grl_vinculada = 0;
                            geraisOk.Add(geral);
                        }
                        else
                        {
                            geral.grl_vinculada = 0;
                            geraisOk.Add(geral);
                        }
                    }
                }
                else
                {
                    geral.grl_vinculada = 0;
                    geraisOk.Add(geral);
                }
            }
            _context.Adequacoes.AddRange(listaAdequacoes);

            _context.Gerais.AddRange(geraisOk);
            await _context.SaveChangesAsync();

            return Ok("Importação feita com sucesso!");
        }
        [HttpDelete]
        public async Task<ActionResult> Delete()
        {
            await _context.Database.ExecuteSqlRawAsync("TRUNCATE TABLE Gerais;");

            return Ok("Tabela limpa com sucesso!");
        }
        [HttpPost("upload-txt")]
        [DisableRequestSizeLimit]
        [RequestFormLimits(MultipartBodyLengthLimit = int.MaxValue, ValueLengthLimit = int.MaxValue)]
        public async Task<IActionResult> UploadTxt(IFormFile file)
        {
            if (file == null || file.Length == 0)
                return BadRequest("Arquivo inválido");

            using (var stream = new StreamReader(file.OpenReadStream()))
            {
                // Expressão regular para capturar objetos JSON que começam com {"ID": e terminam com }
                var regex = new Regex(@"{""ID"":.*?}");

                while (!stream.EndOfStream)
                {
                    var line = await stream.ReadLineAsync();

                    // Encontrar todos os objetos JSON na linha
                    var matches = regex.Matches(line);

                    foreach (Match match in matches)
                    {
                        try
                        {
                            // Obter o JSON limpo diretamente sem substituição de aspas
                            var cleanJson = match.Value;

                            // Verificar se cleanJson está bem formatado para evitar erros de deserialização
                            if (cleanJson.StartsWith("{") && cleanJson.EndsWith("}"))
                            {
                                // Deserializar o JSON diretamente em um objeto Geral
                                var item = JsonConvert.DeserializeObject<Geral>(cleanJson);

                                var geral = new Geral
                                {
                                    grl_id = item.grl_id,
                                    grl_uc = item.grl_uc,
                                    grl_nome = item.grl_nome,
                                    grl_municipio = item.grl_municipio,
                                    grl_nucleo = item.grl_nucleo,
                                    grl_logradouro = item.grl_logradouro,
                                    grl_numero = item.grl_numero,
                                    grl_complemento = item.grl_complemento,
                                    grl_bloco = item.grl_bloco,
                                    grl_cp = item.grl_cp,
                                    grl_cs = item.grl_cs,
                                    grl_pos1 = item.grl_pos1,
                                    grl_pos2 = item.grl_pos2,
                                    grl_pos3 = item.grl_pos3,
                                    grl_et = item.grl_et,
                                    grl_modulo = item.grl_modulo,
                                    grl_display = item.grl_display,
                                    grl_motivo = item.grl_motivo,
                                    grl_empreiteira = item.grl_empreiteira,
                                    grl_status_uc = item.grl_status_uc,
                                    grl_situacao = item.grl_situacao,
                                    grl_gps = item.grl_gps,
                                    grl_vinculada = 0
                                };

                                _context.Gerais.Add(geral);
                                await _context.SaveChangesAsync();
                            }
                        }
                        catch (Exception ex)
                        {
                            return StatusCode(500, $"Erro ao processar o arquivo: {ex.Message}");
                        }
                    }
                }
            }
            return Ok("Arquivo processado com sucesso");
        }
    }
}