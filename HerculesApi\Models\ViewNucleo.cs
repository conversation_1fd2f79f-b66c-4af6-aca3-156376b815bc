﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HerculesApi.Models
{
    public class ViewNucleo
    {
        [Key]
        public int id { get; set; }
        public string nucleo { get; set; }
        public int qtdCorrecao { get; set; }
        public int qtdProvisorio { get; set; }
        public int qtdRetirada { get; set; }
        [NotMapped]
        public int totalServicos { get; set; }
        public string empreiteira { get; set; }
        public int municipioId { get; set; }
    }
}
