using System.ComponentModel.DataAnnotations;

namespace HerculesApi.Models
{
    public class AvulsoCreateDto
    {
        [Required(ErrorMessage = "Nome é obrigatório")]
        public string avu_nome { get; set; }
        
        [Required(ErrorMessage = "Logradouro é obrigatório")]
        public string avu_logradouro { get; set; }
        
        public string? avu_numero { get; set; }
        public string? avu_complemento { get; set; }
        
        public int? avu_cp { get; set; }
        public int? avu_cs { get; set; }
        public int? avu_et { get; set; }
        public int? avu_modulo { get; set; }
        public string? avu_display { get; set; }
        
        public string? avu_pos1 { get; set; }
        public string? avu_pos2 { get; set; }
        public string? avu_pos3 { get; set; }
        
        [Required(ErrorMessage = "Motivo é obrigatório")]
        public int avu_motivoId { get; set; }
        
        public int? avu_municipioId { get; set; }
        public int? avu_nucleoId { get; set; }
        public int? avu_empreiteiraId { get; set; }
        
        [Required(ErrorMessage = "Técnico é obrigatório")]
        public string avu_tecnico { get; set; }
        
        public int? avu_tanque { get; set; }
        public int? avu_uc { get; set; }
    }
}
