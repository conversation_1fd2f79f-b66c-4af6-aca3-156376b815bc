﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using HerculesApi.Database;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using HerculesApi.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.IO;
using System.Runtime.Intrinsics.Arm;
using Microsoft.AspNetCore.Authorization;

namespace HerculesApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class OuvidoriasController : ControllerBase
    {
        private readonly HerculesDbContext _context;
        public OuvidoriasController(HerculesDbContext context)
        {
            _context = context;
        }
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Ouvidoria>>> GetOuvidorias()
        {
            List<Ouvidoria> clientesOrdens = new List<Ouvidoria>();

            var ouvidorias = _context.Ouvidorias.ToList();
            if (ouvidorias.Count > 0)
            {
                foreach (var item in ouvidorias)
                {
                    List<ClienteOuvidoria> listaClientes = new List<ClienteOuvidoria>();
                    List<int> usuids = new List<int>();

                    // Garantir que a lista não é nula
                    if (item.userId == null)
                        item.userId = new List<int>();

                    var ordens = _context.OrdensServico.AsNoTracking().Where(x => x.ord_na == item.ouv_na).ToList();
                    foreach (var o in ordens)
                    {
                        ClienteOuvidoria cli = new ClienteOuvidoria
                        {
                            ouv_titular = o.ord_nome,
                            ouv_logradouro = o.ord_logradouro,
                            ouv_numero = o.ord_numero,
                            ouv_complemento = o.ord_complemento,
                            ouv_cp = o.ord_cp,
                            ouv_uc = o.ord_uc,
                            ouv_modulo = o.ord_modulo,
                            ouv_display = o.ord_display,
                            ouv_et = o.ord_et,
                            ouv_cs = o.ord_cs,
                            ouv_pos1 = o.ord_pos1,
                            ouv_pos2 = o.ord_pos2,
                            ouv_pos3 = o.ord_pos3,
                            ouv_observacao = o.ord_obs
                        };
                        listaClientes.Add(cli);
                    }

                    var relOuvidoriaOrdem = _context.RelOuvidoriasOrdens.AsNoTracking().Where(x => x.ouvId == item.id).ToList();
                    if (relOuvidoriaOrdem.Any())
                    {
                        var rel = _context.RelOrdensTecnicos.Where(x => x.ordemId == relOuvidoriaOrdem[0].ordemId).ToList();
                        foreach (var r in rel)
                        {
                            usuids.Add(r.tecId);
                        }
                    }

                    Ouvidoria clientes = new Ouvidoria
                    {
                        ouv_na = item.ouv_na,
                        id = item.id,
                        ouv_municipio = item.ouv_municipio,
                        ouv_nucleo = item.ouv_nucleo,
                        ouv_observacao = item.ouv_observacao,
                        ouv_data = Convert.ToDateTime(item.ouv_data),
                        ouv_gps = item.ouv_gps,
                        clientes = listaClientes,
                        userId = usuids
                    };
                    clientesOrdens.Add(clientes);
                }
                return clientesOrdens;
            }
            else
            {
                return NotFound("Não há ouvidorias");
            }
        }
        [HttpGet("userid/{userid}")]
        public async Task<ActionResult<IEnumerable<Ouvidoria>>> GetOuvidoriaUsuario(int userid)
        {
            // Obter IDs de ouvidorias vinculadas ao usuário
            var relaOuvidoriaUser = _context.RelOuvidoriasTecnicos
                .AsNoTracking()
                .Where(x => x.tecId == userid)
                .Select(x => x.ouvId)
                .ToList();

            if (!relaOuvidoriaUser.Any())
            {
                return NotFound("Não há ouvidoria vinculada ao Usuário!");
            }

            // Obter ouvidorias vinculadas
            var ouvidorias = _context.Ouvidorias
                .AsNoTracking()
                .Where(x => relaOuvidoriaUser.Contains(x.id) && x.ouv_status == 0)
                .ToList();
            if (ouvidorias != null)
            {
                // Preencher clientes para cada ouvidoria
                foreach (var ouv in ouvidorias)
                {
                    // Inicializar nova lista de clientes para a ouvidoria atual
                    var clientes = new List<ClienteOuvidoria>();

                    var relOuvidoriaOrdem = _context.RelOuvidoriasOrdens
                        .AsNoTracking()
                        .Where(x => x.ouvId == ouv.id)
                        .ToList();

                    foreach (var item in relOuvidoriaOrdem)
                    {
                        var ordemServico = _context.OrdensServico
                            .AsNoTracking()
                            .FirstOrDefault(x => x.id == item.ordemId);

                        if (ordemServico != null)
                        {
                            var cliente = new ClienteOuvidoria
                            {
                                ouv_titular = ordemServico.ord_nome,
                                ouv_logradouro = ordemServico.ord_logradouro,
                                ouv_numero = ordemServico.ord_numero,
                                ouv_complemento = ordemServico.ord_complemento,
                                ouv_cp = ordemServico.ord_cp,
                                ouv_uc = ordemServico.ord_uc,
                                ouv_modulo = ordemServico.ord_modulo,
                                ouv_display = ordemServico.ord_display,
                                ouv_et = ordemServico.ord_et,
                                ouv_cs = ordemServico.ord_cs,
                                ouv_pos1 = ordemServico.ord_pos1,
                                ouv_pos2 = ordemServico.ord_pos2,
                                ouv_pos3 = ordemServico.ord_pos3,
                                ouv_observacao = ordemServico.ord_obs
                            };

                            clientes.Add(cliente);
                        }
                        ouv.ouv_ordId = item.ordemId;
                    }

                    // Associar a lista de clientes à ouvidoria atual
                    ouv.clientes = clientes;
                }

                return ouvidorias;
            }
            else
            {
                return NotFound("Não há ouvidoria ativa!");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<Ouvidoria>> GetOuvidoria(int id)
        {
            List<int> usuids = new List<int>();

            var ouvidoria = _context.Ouvidorias.AsNoTracking().Where(x => x.id == id).FirstOrDefault();
            if (ouvidoria == null)
            {
                return NotFound("Ouvidoria não encontrada");
            }
            var clientes = new List<ClienteOuvidoria>();

            var relOuvidoriaOrdem = _context.RelOuvidoriasOrdens
                .AsNoTracking()
                .Where(x => x.ouvId == ouvidoria.id)
                .ToList();

            foreach (var item in relOuvidoriaOrdem)
            {
                var ordemServico = _context.OrdensServico
                    .AsNoTracking()
                    .FirstOrDefault(x => x.id == item.ordemId);

                if (ordemServico != null)
                {
                    var cliente = new ClienteOuvidoria
                    {
                        ouv_titular = ordemServico.ord_nome,
                        ouv_logradouro = ordemServico.ord_logradouro,
                        ouv_numero = ordemServico.ord_numero,
                        ouv_complemento = ordemServico.ord_complemento,
                        ouv_cp = ordemServico.ord_cp,
                        ouv_uc = ordemServico.ord_uc,
                        ouv_modulo = ordemServico.ord_modulo,
                        ouv_display = ordemServico.ord_display,
                        ouv_et = ordemServico.ord_et,
                        ouv_cs = ordemServico.ord_cs,
                        ouv_pos1 = ordemServico.ord_pos1,
                        ouv_pos2 = ordemServico.ord_pos2,
                        ouv_pos3 = ordemServico.ord_pos3,
                        ouv_observacao = ordemServico.ord_obs
                    };

                    clientes.Add(cliente);
                }
                ouvidoria.ouv_ordId = item.ordemId;
            }

            // Associar a lista de clientes à ouvidoria atual
            ouvidoria.clientes = clientes;
            return ouvidoria;
        }

        [HttpPost("{na}/{obs}/{municipioid}/{nucleoid}/{gps}")]
        public async Task<IActionResult> PostOuvidoria(int na, string obs, int municipioid, int nucleoid, string gps, [FromBody] ClienteDTO cli)
        {
            if (na == 0)
            {
                return BadRequest("NA não pode ser nula ou 0");
            }

            List<RelOrdemTecnico> listaTecnicos = new List<RelOrdemTecnico>();
            List<RelOuvidoriaTecnico> lTecnicosOuvidoria = new List<RelOuvidoriaTecnico>();
            List<OrdemServico> listaOrdem = new List<OrdemServico>();

            var ouv = _context.Ouvidorias
                .Where(x => x.ouv_na.Equals(na))
                .AsNoTracking()
                .FirstOrDefault();

            if (ouv != null)
            {
                return BadRequest(new { message = "Não é possível cadastrar ouvidoria já cadastrada!" });
            }
            else
            {
                var municipio = _context.Municipios.AsNoTracking().Where(x => x.id == municipioid).FirstOrDefault();
                var nucleo = _context.Nucleos.AsNoTracking().Where(x => x.id == nucleoid).FirstOrDefault();
                Ouvidoria ouvidoria = new Ouvidoria();
                if (obs != null)
                {
                    ouvidoria.ouv_na = na;
                    ouvidoria.ouv_data = DateTime.Now;
                    ouvidoria.ouv_observacao = obs;
                    ouvidoria.ouv_municipio = municipio.mun_descricao;
                    ouvidoria.ouv_municipioId = municipioid;
                    ouvidoria.ouv_nucleo = nucleo.nuc_descricao;
                    ouvidoria.nucleoId = nucleoid;
                    ouvidoria.ouv_gps = gps;
                }
                else
                {
                    ouvidoria.ouv_na = na;
                    ouvidoria.ouv_data = DateTime.Now;
                    ouvidoria.ouv_observacao = null;
                    ouvidoria.ouv_municipio = municipio.mun_descricao;
                    ouvidoria.ouv_municipioId = municipioid;
                    ouvidoria.ouv_nucleo = nucleo.nuc_descricao;
                    ouvidoria.nucleoId = nucleoid;
                    ouvidoria.ouv_gps = gps;
                }
                _context.Ouvidorias.Add(ouvidoria);
                await _context.SaveChangesAsync();

                foreach (var item in cli.clientes)
                {
                    OrdemServico ordem = new OrdemServico
                    {
                        ord_na = na,
                        ord_dataEntrada = DateTime.Now,
                        ord_cp = item.ouv_cp,
                        ord_logradouro = item.ouv_logradouro,
                        ord_numero = item.ouv_numero,
                        ord_complemento = item.ouv_complemento,
                        ord_municipio = municipio.mun_descricao,
                        ord_nucleo = nucleo.nuc_descricao,
                        ord_uc = item.ouv_uc,
                        ord_nome = item.ouv_titular,
                        ord_modulo = item.ouv_modulo,
                        ord_display = item.ouv_display,
                        ord_et = item.ouv_et,
                        ord_cs = item.ouv_cs,
                        ord_pos1 = item.ouv_pos1,
                        ord_pos2 = item.ouv_pos2,
                        ord_pos3 = item.ouv_pos3,
                        ord_obs = item.ouv_observacao
                    };

                    listaOrdem.Add(ordem);
                }
                _context.OrdensServico.AddRange(listaOrdem);
                await _context.SaveChangesAsync();

                List<RelOuvidoriaOrdem> listaRelaciona = new List<RelOuvidoriaOrdem>();
                foreach (var item in listaOrdem)
                {
                    RelOuvidoriaOrdem relOuvidoria = new RelOuvidoriaOrdem();
                    relOuvidoria.ouvId = ouvidoria.id;
                    relOuvidoria.ordemId = item.id;
                    listaRelaciona.Add(relOuvidoria);

                    foreach (var user in cli.userId)
                    {
                        // Verifica se já existe o relacionamento para evitar duplicações
                        if (!_context.RelOrdensTecnicos.Any(r => r.ordemId == item.id && r.tecId == user))
                        {
                            RelOrdemTecnico relOrdemTecnico = new RelOrdemTecnico
                            {
                                ordemId = item.id,
                                tecId = user
                            };

                            listaTecnicos.Add(relOrdemTecnico);

                            RelOuvidoriaTecnico relOuv = new RelOuvidoriaTecnico
                            {
                                ouvId = ouvidoria.id,
                                tecId = user
                            };
                            lTecnicosOuvidoria.Add(relOuv);
                        }
                    }
                }

                _context.RelOrdensTecnicos.AddRange(listaTecnicos);
                await _context.SaveChangesAsync();
                _context.RelOuvidoriasTecnicos.AddRange(lTecnicosOuvidoria);
                await _context.SaveChangesAsync();
                _context.RelOuvidoriasOrdens.AddRange(listaRelaciona);
                await _context.SaveChangesAsync();

                return Ok("Ouvidoria cadastrada com sucesso!");
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> PutOuvidoria(int id, Ouvidoria ouvidoria)
        {
            var ouv = _context.Ouvidorias.AsNoTracking().Where(x => x.id == id).FirstOrDefault();
            if (ouv == null)
            {
                return BadRequest("Ouvidoria não encontrada");
            }

            _context.Entry(ouvidoria).State = EntityState.Modified;
            try
            {
                await _context.SaveChangesAsync();
                var relOuvidoriaOrdem = _context.RelOuvidoriasOrdens.AsNoTracking().Where(x => x.ouvId == ouv.id).ToList();
                foreach (var item in relOuvidoriaOrdem)
                {
                    var ordem = _context.OrdensServico.AsNoTracking().Where(x => x.id == item.ordemId).FirstOrDefault();
                    ordem.ord_na = ouvidoria.ouv_na;
                    _context.OrdensServico.Update(ordem);
                    await _context.SaveChangesAsync();
                }
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!OuvidoriaExists(id))
                {
                    return NotFound("Ouvidoria não encontrada");
                }
                else
                {
                    throw;
                }
            }

            return Ok("Ouvidoria alterada com sucesso!");
        }
        [HttpPut("status/{id}")]
        public async Task<IActionResult> PutOuvidoriaStatus(int id, int status)
        {
            var ouv = _context.Ouvidorias.Where(x => x.id == id).AsNoTracking().FirstOrDefault();
            if (ouv == null)
            {
                return BadRequest("Ouvidoria não encontrada");
            }
            try
            {
                ouv.ouv_status = status;
                _context.Ouvidorias.Update(ouv);
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                return NotFound("Ouvidoria não pode ser editada");
            }

            return Ok("Ouvidoria alterada com sucesso!");
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteOuvidoria(int id)
        {
            var ouvidoria = await _context.Ouvidorias.FindAsync(id);
            if (ouvidoria == null)
            {
                return NotFound("Ouvidoria não encontrada");
            }
            else
            {
                var ordens = _context.OrdensServico.AsNoTracking().Where(x => x.ord_na == ouvidoria.ouv_na).ToList();
                if (ordens != null)
                {
                    _context.OrdensServico.RemoveRange(ordens);
                    await _context.SaveChangesAsync();
                }
                _context.Ouvidorias.Remove(ouvidoria);
                await _context.SaveChangesAsync();

                return Ok("Ouvidoria excluida com sucesso!");
            }
        }
        private bool OuvidoriaExists(int id)
        {
            return _context.Ouvidorias.Any(e => e.id == id);
        }
    }
}