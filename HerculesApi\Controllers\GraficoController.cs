﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using HerculesApi.Database;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using HerculesApi.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Microsoft.AspNetCore.Authorization;

namespace HerculesApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class GraficoController : ControllerBase
    {
        private readonly HerculesDbContext _context;
        public GraficoController(HerculesDbContext context)
        {
            _context = context;
        }
        [HttpGet("grafico/periodo/{dtinicial}/{dtfinal}")]
        public async Task<ActionResult<Grafico>> GetDadosGrafico(DateTime dtinicial, DateTime dtfinal)
        {
            dtinicial = new DateTime(dtinicial.Year, dtinicial.Month, dtinicial.Day, 0, 1, 0);
            dtfinal = new DateTime(dtfinal.Year, dtfinal.Month, dtfinal.Day, 23, 59, 0);

            List<TipoServicoDTO> listaServico = new List<TipoServicoDTO>();
            int qtdCorrecao = 0;
            int qtdProvisorio = 0;
            int qtdAntigo = 0;
            int qtdOuvidoria = 0;

            Grafico grafico = new Grafico();
            var visitas = _context.Visitas.AsNoTracking().Where(x => x.vis_data >= dtinicial && x.vis_data <= dtfinal).ToList();
            var agendamentos = _context.Agendamentos.AsNoTracking().Where(y=>y.agd_dtVisita >= dtinicial && y.agd_dtVisita <=dtfinal).ToList();
            grafico.totalAgendamentos = agendamentos.Count;
            if (visitas.Count()==0)
            {
                return grafico;
            }
            var visEf = visitas.Where(x => x.vis_efetivado == 1).Select(b => b.id).ToList();
            var visNaoEf = visitas.Where(x => x.vis_efetivado == 0).Select(b => b.id).ToList();
            var tipoCorrecao = _context.TiposDeCorrecao.AsNoTracking().ToList();
            var visitasIds = visitas.Select(x => x.id).ToList();

            var motivosComQtd = _context.RelVisitasMotivos
                .Where(rvm => visNaoEf.Contains(rvm.visId))
                .GroupBy(rvm => rvm.motId)
                .Select(g => new Motivo
                {
                    id = g.Key,
                    mot_descricao = _context.Motivos.Where(m => m.id == g.Key).Select(m => m.mot_descricao).FirstOrDefault(),
                    qtd = g.Count()
                })
                .ToList();

            var ordensServico = _context.OrdensServico.AsNoTracking()
                    .Where(os => _context.RelVisitasOrdens.Any(rvo => visitasIds.Contains(rvo.visId) && rvo.ordemId == os.id))
                    .ToList();

            var clientesEfetivados = ordensServico
                    .Where(os => _context.RelVisitasOrdens.Any(rvo => visEf.Contains(rvo.visId) && rvo.ordemId == os.id))
                    .ToList();
            var clientesNaoEfetivados = ordensServico
                    .Where(os => _context.RelVisitasOrdens.Any(rvo => visNaoEf.Contains(rvo.visId) && rvo.ordemId == os.id))
                    .ToList();

            decimal diasPeriodo = ordensServico
                    .Where(os => os.ord_dataFechamento.HasValue)
                    .Select(os => os.ord_dataFechamento.Value.Date)
                    .Distinct()
                    .Count();
            foreach (var item in ordensServico)
            {
                if (!string.IsNullOrEmpty(item.ord_motivo))
                {
                    item.ord_tipoServ = "CORREÇÃO";
                    item.motivo = item.ord_motivo;
                    qtdCorrecao = qtdCorrecao + 1;
                }
                else if (item.ord_nome == "CLIENTE PROVISÓRIO")
                {
                    item.ord_tipoServ = "CLIENTE PROVISÓRIO";
                    item.motivo = item.ord_tipoServ;
                    qtdProvisorio = qtdProvisorio + 1;
                }
                else if (item.ord_status_uc == "AGUARDA RETIRADA DE MEDIDOR")
                {
                    item.ord_tipoServ = "RETIRADA DE MEDIDOR";
                    item.motivo = item.ord_tipoServ;
                    qtdAntigo = qtdAntigo + 1;
                }
                else if (item.ord_na > 0)
                {
                    item.ord_tipoServ = "OUVIDORIA";
                    item.motivo = item.ord_tipoServ;
                    qtdOuvidoria = qtdOuvidoria + 1;
                }
            }
            // Carrega primeiro os IDs de empreiteiras distintas na memória
            var empreiteirasDistintas = ordensServico
                .Select(os => os.ord_empreiteira)
                .Distinct()
                .ToList(); // Força execução para liberar a conexão do banco

            // Carrega as empreiteiras do banco (já filtrando)
            var empreiteiras = _context.Empreiteiras
                .Where(emp => empreiteirasDistintas.Contains(emp.emp_desc))
                .ToList(); // Executa antes para evitar múltiplas conexões

            // Agora, faz os cálculos em memória
            var empreiteirasComQtd = empreiteiras.Select(emp => new Empreiteira
            {
                id = emp.id,
                emp_desc = emp.emp_desc,
                qtdVisitas = ordensServico.Count(os => os.ord_empreiteira == emp.emp_desc),

                // Carrega primeiro os equipamentos retirados
                medidoresRetirados = _context.RelVisitasEquipamentos.AsNoTracking()
    .Where(rve => rve.situacao == false &&
                  _context.RelEquipEmpreiteiras
                      .Any(ree => ree.ree_idEmp == emp.id && ree.ree_idEquip == rve.eqpId) &&
                  _context.EquipamentosVisitas
                      .Any(eq => eq.id == rve.eqpId && eq.eqv_descricao == "medidor convencional"))
    .Count(),


                // Carrega primeiro os equipamentos instalados
                medidoresInstalados = _context.RelVisitasEquipamentos
                    .Where(rve => rve.situacao == true)
                    .Join(_context.RelEquipEmpreiteiras, rve => rve.eqpId, ree => ree.ree_idEquip,
                        (rve, ree) => new { rve, ree })
                    .Where(joined => joined.ree.ree_idEmp == emp.id)
                    .Join(_context.EquipamentosVisitas, joined => joined.rve.eqpId, eq => eq.id,
                        (joined, eq) => eq)
                    .Count(eq => eq.eqv_descricao == "medidor convencional")
            }).ToList();

            var testQuery = _context.RelVisitasEquipamentos
    .Where(rve => rve.situacao == false &&
                  _context.RelEquipEmpreiteiras
                      .Any(ree => ree.ree_idEmp == 3 && ree.ree_idEquip == rve.eqpId) &&
                  _context.EquipamentosVisitas
                      .Any(eq => eq.id == rve.eqpId && eq.eqv_descricao == "medidor convencional"))
    .ToList();

            




            TipoServicoDTO correcao = new TipoServicoDTO();
            correcao.servico = "CORREÇÃO";
            correcao.qtd = qtdCorrecao;
            listaServico.Add(correcao);

            TipoServicoDTO provisorio = new TipoServicoDTO();
            provisorio.servico = "CLIENTE PROVISÓRIO";
            provisorio.qtd = qtdProvisorio;
            listaServico.Add(provisorio);

            TipoServicoDTO antigo = new TipoServicoDTO();
            antigo.servico = "MEDIDOR ANTIGO";
            antigo.qtd = qtdAntigo;
            listaServico.Add(antigo);

            TipoServicoDTO ouvidoria = new TipoServicoDTO();
            ouvidoria.servico = "OUVIDORIA";
            ouvidoria.qtd = qtdOuvidoria;
            listaServico.Add(ouvidoria);

            grafico.efetivados = clientesEfetivados.Count();
            grafico.naoEfetivados = clientesNaoEfetivados.Count();
            grafico.porcEfetivados = 0;
            grafico.porcNaoEfetivados = 0;
            grafico.totalVisitas = ordensServico.Count();

            if (grafico.efetivados > 0)
            {
                grafico.porcEfetivados = (grafico.efetivados * 100) / grafico.totalVisitas;
            }            
            if (grafico.naoEfetivados > 0)
            {
                grafico.porcNaoEfetivados = (grafico.naoEfetivados * 100) / grafico.totalVisitas;
            }


            grafico.efetivadosTipo = listaServico;
            grafico.naoEfetivadosMotivo = motivosComQtd;
            
            grafico.mediaDiaria = grafico.totalVisitas / diasPeriodo;
            grafico.mediaEquipe = (grafico.totalVisitas / diasPeriodo) / 3;
            grafico.empreiteiras = empreiteirasComQtd;
            return grafico;
        }
    }
}