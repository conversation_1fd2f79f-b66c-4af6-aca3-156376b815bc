{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\Github\\FrotasCoreApi\\FrotasCoreApi\\FrotasCoreApi.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\Github\\FrotasCoreApi\\FrotasCoreApi\\FrotasCoreApi.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\Github\\FrotasCoreApi\\FrotasCoreApi\\FrotasCoreApi.csproj", "projectName": "FrotasCoreApi", "projectPath": "C:\\Users\\<USER>\\source\\Github\\FrotasCoreApi\\FrotasCoreApi\\FrotasCoreApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\Github\\FrotasCoreApi\\FrotasCoreApi\\obj\\publish\\linux-x64\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.AspNet.Identity.Core": {"target": "Package", "version": "[2.2.3, )"}, "Microsoft.AspNet.Identity.Owin": {"target": "Package", "version": "[2.2.3, )"}, "Microsoft.AspNet.WebApi.Core": {"target": "Package", "version": "[5.2.9, )"}, "Microsoft.AspNetCore.Authentication": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[6.0.11, )"}, "Microsoft.AspNetCore.Identity": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[6.0.11, )"}, "Microsoft.AspNetCore.Identity.UI": {"target": "Package", "version": "[6.0.11, )"}, "Microsoft.AspNetCore.JsonPatch": {"target": "Package", "version": "[6.0.11, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[6.0.11, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.11, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[6.0.11, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[6.0.11, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.11, )"}, "Microsoft.VisualStudio.Web.CodeGeneration.Design": {"target": "Package", "version": "[6.0.11, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[6.0.2, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.NETCore.App.Host.linux-x64", "version": "[6.0.9, 6.0.9]"}], "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.401\\RuntimeIdentifierGraph.json"}}, "runtimes": {"linux-x64": {"#import": []}}}}}