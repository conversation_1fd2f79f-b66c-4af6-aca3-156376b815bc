{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\FrotasCoreApi\\mev\\mv.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\FrotasCoreApi\\mev\\mv.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\FrotasCoreApi\\mev\\mv.csproj", "projectName": "mv", "projectPath": "C:\\Users\\<USER>\\source\\repos\\FrotasCoreApi\\mev\\mv.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\FrotasCoreApi\\mev\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net5.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "dependencies": {"Microsoft.AspNet.Identity.Core": {"target": "Package", "version": "[2.2.3, )"}, "Microsoft.AspNet.Identity.Owin": {"target": "Package", "version": "[2.2.3, )"}, "Microsoft.AspNet.WebApi.Core": {"target": "Package", "version": "[5.2.9, )"}, "Microsoft.AspNetCore.Authentication": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[5.0.17, )"}, "Microsoft.AspNetCore.Identity": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[5.0.17, )"}, "Microsoft.AspNetCore.Identity.UI": {"target": "Package", "version": "[5.0.17, )"}, "Microsoft.AspNetCore.JsonPatch": {"target": "Package", "version": "[5.0.16, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[5.0.17, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[5.0.17, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[5.0.17, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[5.0.17, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[5.0.17, )"}, "Microsoft.VisualStudio.Web.CodeGeneration.Design": {"target": "Package", "version": "[5.0.2, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[5.0.4, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[5.6.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[5.0.17, 5.0.17]"}], "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.401\\RuntimeIdentifierGraph.json"}}}}}