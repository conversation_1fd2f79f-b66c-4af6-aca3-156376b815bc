﻿using HerculesApi.Models;
using HerculesApi.Services;
using HerculesApi.Database;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using DocumentFormat.OpenXml.Bibliography;

namespace HerculesApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class LoginController : ControllerBase
    {
        private readonly AdmDbContext _context;
        private readonly HerculesDbContext _contexthercules;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly UserManager<ApplicationUser> _userManager;
        public LoginController(AdmDbContext context, HerculesDbContext contexthercules, SignInManager<ApplicationUser> signInManager, UserManager<ApplicationUser> userManager)
        {
            _context = context;
            _contexthercules = contexthercules;
            _signInManager = signInManager;
            _userManager = userManager;
        }
        [HttpPost]
        [Route("Login")]
        [AllowAnonymous]
        public async Task<IActionResult> PostAsync(
            [FromServices] HerculesDbContext context, [FromBody] UserLogin userLogin)
        {

            var userUser = _context.Users.FirstOrDefault(x => x.Email == userLogin.Email);
            if (userUser == null)
            {
                userUser = _context.Users.FirstOrDefault(x => x.NickName == userLogin.Email);
                if (userUser == null)
                {

                    return BadRequest(new
                    {
                        msg = "Tentativa de login inválida"
                    });
                }
            }
            var relacionamento = _context.RelUsersAplicacoes.AsNoTracking().Where(x => x.UserId == userUser.UserId && x.AplicacaoId == 1).FirstOrDefault();
            if (relacionamento == null)
            {

                return BadRequest(new
                {
                    msg = "Usuário não possui permissão para uso do aplicativo!"
                });
            }
            if (userUser != null && relacionamento != null)
            {
                if (userUser.Status != true)
                {
                    return BadRequest(new
                    {
                        msg = "Usuário desativado."
                    });
                }

                var signInResult = await _signInManager.CheckPasswordSignInAsync(userUser, userLogin.Password, false);

                if (signInResult.Succeeded)
                {
                    try
                    {
                        if (userUser.Status == false)
                        {
                            return BadRequest(new
                            {
                                msg = "Usuário desativado."
                            });
                        }
                        else
                        {
                            try
                            {
                                string roleId = _context.UserRoles.FirstOrDefault(x => x.UserId == userUser.Id).RoleId;

                                var usuarioN = new UserLogin()
                                {
                                    Email = userUser.Email,
                                    Password = "######",
                                    Id = userUser.UserId,
                                    Nome = userUser.FirstName,
                                    NickName = userUser.NickName,
                                    Tipo = _context.Roles.FirstOrDefault(r => r.Id == roleId).Name,
                                    Status = userUser.Status,
                                };

                                string version = userLogin.Nome;

                                UserLogin n_user = usuarioN;

                                //var token = TokenService.GenerateTokenUser(n_user);
                                //n_user.Token = token;

                                //return Ok(n_user);
                                var versao = _contexthercules.Versionamentos.Where(x => x.status == true).ToList();
                                
                                if (versao != null)
                                {
                                    var versaoAtual = versao.Last();
                                    foreach (var ver in versao)
                                    {
                                        if (version.Equals(ver.versao))
                                        {
                                            var token = TokenService.GenerateTokenUser(n_user);
                                            n_user.Token = token;

                                            return Ok(n_user);
                                        }
                                    }
                                    return BadRequest(new
                                    {
                                        msg = $"A versão '{userLogin.Nome}' do aplicativo foi descontinuada. Atualize o aplicativo para a versão '{versaoAtual.versao}'!"
                                    });

                                }
                                else
                                {
                                    return BadRequest(new
                                    {
                                        msg = $"A versão '{userLogin.Nome}' do aplicativo foi desativada, contate o administrador!"
                                    });
                                }


                            }
                            catch (Exception e)
                            {
                                return BadRequest(new
                                {
                                    msg = "Usuário não possui permissão para uso do aplicativo!"
                                });
                            }
                        }
                    }
                    catch
                    {
                        return BadRequest(new
                        {
                            msg = "Usuário não possui permissão para uso do aplicativo!"
                        });
                    }
                }
                else
                {
                    return BadRequest(new
                    {
                        msg = "Tentativa de login inválida"
                    });//senha incorreta
                }
            }
            else
            {
                return BadRequest(new
                {
                    msg = "Tentativa de login inválida"
                });//e-mail não cadastrado
            }
        }
        [HttpPost]
        [Route("Login/Web")]
        [AllowAnonymous]
        public async Task<IActionResult> PostWebAsync(
            [FromServices] HerculesDbContext context, [FromBody] UserLogin userLogin)
        {

            var userUser = _context.Users.FirstOrDefault(x => x.Email == userLogin.Email);
            if (userUser == null)
            {
                userUser = _context.Users.FirstOrDefault(x => x.NickName == userLogin.Email);
                if (userUser == null)
                {

                    return BadRequest(new
                    {
                        msg = "Tentativa de login inválida"
                    });
                }
            }
            var relacionamento = _context.RelUsersAplicacoes.AsNoTracking().Where(x => x.UserId == userUser.UserId && x.AplicacaoId == 1).FirstOrDefault();
            if (relacionamento == null)
            {

                return BadRequest(new
                {
                    msg = "Usuário não possui permissão para uso do aplicativo!"
                });
            }
            if (userUser != null && relacionamento != null)
            {
                if (userUser.Status != true)
                {
                    return BadRequest(new
                    {
                        msg = "Usuário desativado."
                    });
                }

                var signInResult = await _signInManager.CheckPasswordSignInAsync(userUser, userLogin.Password, false);

                if (signInResult.Succeeded)
                {
                    try
                    {
                        if (userUser.Status == false)
                        {
                            return BadRequest(new
                            {
                                msg = "Usuário desativado."
                            });
                        }
                        else
                        {
                            try
                            {
                                string roleId = _context.UserRoles.FirstOrDefault(x => x.UserId == userUser.Id).RoleId;

                                var usuarioN = new UserLogin()
                                {
                                    Email = userUser.Email,
                                    Password = "######",
                                    Id = userUser.UserId,
                                    Nome = userUser.FirstName,
                                    NickName = userUser.NickName,
                                    Tipo = _context.Roles.FirstOrDefault(r => r.Id == roleId).Name,
                                    Status = userUser.Status,
                                };

                                string version = userLogin.Nome;

                                UserLogin n_user = usuarioN;

                                var token = TokenService.GenerateTokenUser(n_user);
                                n_user.Token = token;

                                return Ok(n_user);

                            }
                            catch (Exception e)
                            {
                                return BadRequest(new
                                {
                                    msg = "Usuário não possui permissão para uso do aplicativo!"
                                });
                            }
                        }
                    }
                    catch
                    {
                        return BadRequest(new
                        {
                            msg = "Usuário não possui permissão para uso do aplicativo!"
                        });
                    }
                }
                else
                {
                    return BadRequest(new
                    {
                        msg = "Tentativa de login inválida"
                    });//senha incorreta
                }
            }
            else
            {
                return BadRequest(new
                {
                    msg = "Tentativa de login inválida"
                });//e-mail não cadastrado
            }
        }
    }
}
