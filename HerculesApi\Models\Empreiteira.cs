﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HerculesApi.Models
{
    public class Empreiteira
    {
        [Key]
        public int id { get; set; }
        public string emp_desc { get; set; }
        [NotMapped]
        public int medidoresRetirados { get; set; }
        [NotMapped]
        public int medidoresInstalados { get; set; }
        [NotMapped]
        public int qtdVisitas { get; set; }
    }
}
