﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using HerculesApi.Database;
using HerculesApi.Models;
using Microsoft.AspNetCore.Authorization;

namespace HerculesApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class VersionamentosController : ControllerBase
    {
        private readonly HerculesDbContext _context;

        public VersionamentosController(HerculesDbContext context)
        {
            _context = context;
        }
                
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Versionamento>>> GetVersoes()
        {
            return await _context.Versionamentos.ToListAsync();
        }
        
        [HttpGet("{id}")]
        public async Task<ActionResult<Versionamento>> GetVersao(int id)
        {
            var versao = await _context.Versionamentos.FindAsync(id);

            if (versao == null)
            {
                return NotFound();
            }

            return versao;
        }

       
        [HttpPut("{id}")]
        public async Task<IActionResult> PutVersao(int id, Versionamento versao)
        {
            if (id != versao.id)
            {
                return BadRequest();
            }

            _context.Entry(versao).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!VersaoExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        [HttpPost]
        public async Task<ActionResult<Versionamento>> PostVersao(Versionamento versao)
        {
            _context.Versionamentos.Add(versao);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetVersao", new { id = versao.id }, versao);
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteVersao(int id)
        {
            var versao = await _context.Versionamentos.FindAsync(id);
            if (versao == null)
            {
                return NotFound();
            }

            _context.Versionamentos.Remove(versao);
            await _context.SaveChangesAsync();

            return NoContent();
        }               

        private bool VersaoExists(int id)
        {
            return _context.Versionamentos.Any(e => e.id == id);
        }
    }
}
