﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using HerculesApi.Database;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using HerculesApi.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Microsoft.AspNetCore.Authorization;

namespace HerculesApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class EquipamentosController : ControllerBase
    {
        private readonly HerculesDbContext _context;
        public EquipamentosController(HerculesDbContext context)
        {
            _context = context;
        }
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Equipamento>>> GetEquipamentos()
        {
            var equipamentos = _context.Equipamentos.ToList();
            if (equipamentos.Count > 0)
            {
                return equipamentos;
            }
            else
            {
                return NotFound("Não há equipamentos");
            }
        }
        [HttpGet("{id}")]
        public async Task<ActionResult<Equipamento>> GetEquipamento(int id)
        {
            var equipamento = await _context.Equipamentos.FindAsync(id);
            if (equipamento == null)
            {
                return NotFound("Equipamento não encontrado");
            }

            return equipamento;
        }
        [HttpPost]
        public async Task<ActionResult<Equipamento>> PostEquipamento(Equipamento equipamento)
        {
            var equip = _context.Equipamentos
                .Where(x => x.eqp_descricao.ToLower().Equals(equipamento.eqp_descricao.ToLower()))
                .AsNoTracking()
                .FirstOrDefault();
            if (equip != null)
            {
                return BadRequest(new { message = "Não é possível cadastrar equipamento já cadastrado!" });
            }
            else
            {
                _context.Equipamentos.Add(equipamento);
                await _context.SaveChangesAsync();

                return Ok("Equipamento cadastrado com sucesso!");
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> PutEquipamento(int id, Equipamento equipamento)
        {
            var equ = _context.Equipamentos.Where(x => x.id == id).FirstOrDefault();
            if (equ == null)
            {
                return BadRequest("Equipamento não encontrado");
            }

            _context.Entry(equipamento).State = EntityState.Modified;
            try
            {
                await _context.SaveChangesAsync();                
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!EquipamentoExists(id))
                {
                    return NotFound("Equipamento não encontrado");
                }
                else
                {
                    throw;
                }
            }

            return Ok("Equipamento alterado com sucesso!");
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteEquipamento(int id)
        {
            var equipamento = await _context.Equipamentos.FindAsync(id);
            if (equipamento == null)
            {
                return NotFound("Equipamento não encontrado");
            }
            else
            {
                _context.Equipamentos.Remove(equipamento);
                await _context.SaveChangesAsync();

                return Ok("Equipamento excluido com sucesso!");
            }

        }
        private bool EquipamentoExists(int id)
        {
            return _context.Equipamentos.Any(e => e.id == id);
        }
    }
}