[{"ContainingType": "ServicosEletApi.Controllers.GeraisController", "Method": "GetGeral", "RelativePath": "api/<PERSON>is", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[ServicosEletApi.Models.Geral, ServicosEletApi, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServicosEletApi.Controllers.GeraisController", "Method": "PostFuncao", "RelativePath": "api/<PERSON>is", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "gerais", "Type": "System.Collections.Generic.List`1[[ServicosEletApi.Models.Geral, ServicosEletApi, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "ServicosEletApi.Models.Geral", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServicosEletApi.Controllers.GeraisController", "Method": "Delete", "RelativePath": "api/<PERSON>is", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ServicosEletApi.Controllers.LogErrosController", "Method": "GetLogErros", "RelativePath": "api/Log<PERSON>rros", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[ServicosEletApi.Models.LogErro, ServicosEletApi, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServicosEletApi.Controllers.LogErrosController", "Method": "PostLogErro", "RelativePath": "api/Log<PERSON>rros", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "logErro", "Type": "ServicosEletApi.Models.LogErro", "IsRequired": true}], "ReturnTypes": [{"Type": "ServicosEletApi.Models.LogErro", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServicosEletApi.Controllers.LogErrosController", "Method": "GetLogErro", "RelativePath": "api/LogErros/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ServicosEletApi.Models.LogErro", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServicosEletApi.Controllers.LogErrosController", "Method": "PutLogErro", "RelativePath": "api/LogErros/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "logErro", "Type": "ServicosEletApi.Models.LogErro", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ServicosEletApi.Controllers.LogErrosController", "Method": "DeleteLogErro", "RelativePath": "api/LogErros/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ServicosEletApi.Controllers.LoginController", "Method": "PostAsync", "RelativePath": "api/Login/Login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userLogin", "Type": "ServicosEletApi.Models.UserLogin", "IsRequired": true}], "ReturnTypes": []}]