﻿using HerculesApi.Controllers;
using HerculesApi.Models;
using HerculesApi.Models;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace HerculesApi.Database
{
    public class HerculesDbContext : IdentityDbContext<ApplicationUser>
    {
        public HerculesDbContext(DbContextOptions<HerculesDbContext> options) : base(options)
        {

        }
        public DbSet<Adequacao> Adequacoes { get; set; }
        public DbSet<Avulso> Avulsos { get; set; }
        public DbSet<Agendamento> Agendamentos { get; set; }
        public DbSet<Arquivo> Arquivos { get; set; }
        public DbSet<Empreiteira> Empreiteiras { get; set; }
        public DbSet<Equipamento> Equipamentos { get; set; }
        public DbSet<EquipamentoVisita> EquipamentosVisitas { get; set; }
        public DbSet<Foto> Fotos { get; set; }
        public DbSet<Geral> Gerais { get; set; }
        public DbSet<Justificativa> Justificativas { get; set; }
        public DbSet<LogErro> LogErros { get; set; }
        public DbSet<Municipio> Municipios { get; set; }
        public DbSet<Motivo> Motivos { get; set; }
        public DbSet<MotivoAvulso> MotivosAvulso { get; set; }
        public DbSet<Nucleo> Nucleos { get; set; }
        public DbSet<Ouvidoria> Ouvidorias { get; set; }
        public DbSet<OrdemServico> OrdensServico { get; set; }
        public DbSet<RelAgendaTecnico> RelAgendTecnicos { get; set; }
        public DbSet<RelAgendaTipoServico> RelAgendTipoServicos { get; set; }
        public DbSet<RelArquivoOuvidoria> RelArquivosOuvidorias { get; set; }
        public DbSet<RelArquivoAgend> RelArquivosAgend { get; set; }
        public DbSet<RelEquipEmpreiteira> RelEquipEmpreiteiras { get; set; }
        public DbSet<RelEquipVisitaFoto> RelEquipVisitaFotos { get; set; }
        public DbSet<RelNucleoEmpreiteira> RelNucleosEmpreiteiras { get; set; }
        public DbSet<RelMunicipioNucleo> RelMunicipiosNucleos { get; set; }
        public DbSet<RelOrdemTecnico> RelOrdensTecnicos { get; set; }
        public DbSet<RelOuvidoriaOrdem> RelOuvidoriasOrdens{ get; set; }
        public DbSet<RelOuvidoriaTecnico> RelOuvidoriasTecnicos { get; set; }
        public DbSet<RelVisitaAvulso> RelVisitasAvulsos{ get; set; }
        public DbSet<RelVisitaEquipamento> RelVisitasEquipamentos { get; set; }
        public DbSet<RelVisitaFoto> RelVisitasFotos { get; set; }
        public DbSet<RelVisitaMotivo> RelVisitasMotivos { get; set; }
        public DbSet<RelVisitaOrdem> RelVisitasOrdens { get; set; }
        public DbSet<TipoCorrecao> TiposDeCorrecao { get; set; }
        public DbSet<TipoServico> TiposDeServico { get; set; }
        public DbSet<Versionamento> Versionamentos { get; set; }
        public DbSet<Visita> Visitas { get; set; }
        public DbSet<ViewAvulso> vw_avulsos { get; set; }
        public DbSet<ViewMunicipio> vw_municipios { get; set; }
        public DbSet<ViewOrdemServico> vw_ordens_servico { get; set; }
        public DbSet<ViewNucleo> vw_nucleos { get; set; }
        public DbSet<ViewResumo> vw_resumo { get; set; }
        public DbSet<ViewVisita> vw_visitas { get; set; }
    }
}
