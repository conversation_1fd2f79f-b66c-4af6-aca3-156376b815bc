@echo off
echo =====================================================
echo CONFIGURANDO MYSQL NO WINDOWS
echo =====================================================

echo.
echo PASSO 1: Procurando instalacao do MySQL...
echo.

REM Verificar locais comuns do MySQL
set MYSQL_PATHS[0]="C:\Program Files\MySQL\MySQL Server 8.0\bin"
set MYSQL_PATHS[1]="C:\Program Files\MySQL\MySQL Server 5.7\bin"
set MYSQL_PATHS[2]="C:\Program Files (x86)\MySQL\MySQL Server 8.0\bin"
set MYSQL_PATHS[3]="C:\Program Files (x86)\MySQL\MySQL Server 5.7\bin"
set MYSQL_PATHS[4]="C:\xampp\mysql\bin"
set MYSQL_PATHS[5]="C:\wamp64\bin\mysql\mysql8.0.21\bin"
set MYSQL_PATHS[6]="C:\laragon\bin\mysql\mysql-8.0.30-winx64\bin"

echo Verificando caminhos comuns do MySQL:
echo.

if exist "C:\Program Files\MySQL\MySQL Server 8.0\bin\mysqldump.exe" (
    echo [ENCONTRADO] C:\Program Files\MySQL\MySQL Server 8.0\bin
    set MYSQL_PATH=C:\Program Files\MySQL\MySQL Server 8.0\bin
    goto :found
)

if exist "C:\Program Files\MySQL\MySQL Server 5.7\bin\mysqldump.exe" (
    echo [ENCONTRADO] C:\Program Files\MySQL\MySQL Server 5.7\bin
    set MYSQL_PATH=C:\Program Files\MySQL\MySQL Server 5.7\bin
    goto :found
)

if exist "C:\Program Files (x86)\MySQL\MySQL Server 8.0\bin\mysqldump.exe" (
    echo [ENCONTRADO] C:\Program Files (x86)\MySQL\MySQL Server 8.0\bin
    set MYSQL_PATH=C:\Program Files (x86)\MySQL\MySQL Server 8.0\bin
    goto :found
)

if exist "C:\xampp\mysql\bin\mysqldump.exe" (
    echo [ENCONTRADO] C:\xampp\mysql\bin
    set MYSQL_PATH=C:\xampp\mysql\bin
    goto :found
)

if exist "C:\wamp64\bin\mysql\mysql8.0.21\bin\mysqldump.exe" (
    echo [ENCONTRADO] C:\wamp64\bin\mysql\mysql8.0.21\bin
    set MYSQL_PATH=C:\wamp64\bin\mysql\mysql8.0.21\bin
    goto :found
)

if exist "C:\laragon\bin\mysql\mysql-8.0.30-winx64\bin\mysqldump.exe" (
    echo [ENCONTRADO] C:\laragon\bin\mysql\mysql-8.0.30-winx64\bin
    set MYSQL_PATH=C:\laragon\bin\mysql\mysql-8.0.30-winx64\bin
    goto :found
)

echo [NAO ENCONTRADO] MySQL nao foi encontrado nos caminhos comuns.
echo.
echo OPCOES:
echo 1. Instalar MySQL Community Server
echo 2. Usar XAMPP/WAMP/Laragon
echo 3. Procurar manualmente
echo.
goto :manual

:found
echo.
echo =====================================================
echo MySQL ENCONTRADO!
echo Caminho: %MYSQL_PATH%
echo =====================================================
echo.

echo PASSO 2: Testando conexao...
"%MYSQL_PATH%\mysql.exe" -h *************** -u erickson -p"B@r@m@i@0824*" -e "SELECT 'Conexao OK' as status;"

if %errorlevel% neq 0 (
    echo ERRO: Nao foi possivel conectar ao banco!
    echo Verifique usuario/senha/host
    pause
    exit /b 1
)

echo.
echo PASSO 3: Executando backup...
"%MYSQL_PATH%\mysqldump.exe" -h *************** -u erickson -p"B@r@m@i@0824*" bd_hercules > backup_producao.sql

if %errorlevel% neq 0 (
    echo ERRO: Falha ao fazer backup!
    pause
    exit /b 1
)

echo SUCESSO: Backup criado!

echo.
echo PASSO 4: Recriando banco de teste...
"%MYSQL_PATH%\mysql.exe" -h *************** -u erickson -p"B@r@m@i@0824*" -e "DROP DATABASE IF EXISTS bd_hercules_teste;"
"%MYSQL_PATH%\mysql.exe" -h *************** -u erickson -p"B@r@m@i@0824*" -e "CREATE DATABASE bd_hercules_teste;"

echo.
echo PASSO 5: Importando dados...
"%MYSQL_PATH%\mysql.exe" -h *************** -u erickson -p"B@r@m@i@0824*" bd_hercules_teste < backup_producao.sql

if %errorlevel% neq 0 (
    echo ERRO: Falha ao importar dados!
    pause
    exit /b 1
)

echo.
echo =====================================================
echo SUCESSO! Banco copiado com sucesso!
echo =====================================================
echo.
echo Agora teste sua API!
echo.
pause
exit /b 0

:manual
echo.
echo INSTRUCOES PARA ENCONTRAR O MYSQL MANUALMENTE:
echo.
echo 1. Abra o Explorador de Arquivos
echo 2. Procure por 'mysqldump.exe' no disco C:
echo 3. Anote o caminho completo da pasta 'bin'
echo 4. Execute este comando substituindo o caminho:
echo.
echo "C:\CAMINHO\PARA\MYSQL\bin\mysqldump.exe" -h *************** -u erickson -p"B@r@m@i@0824*" bd_hercules ^> backup_producao.sql
echo.
echo OU instale o MySQL Community Server:
echo https://dev.mysql.com/downloads/mysql/
echo.
pause