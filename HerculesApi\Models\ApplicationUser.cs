﻿using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Threading.Tasks;

namespace HerculesApi.Models
{
    public class ApplicationUser : IdentityUser
    {
        public string FirstName { get; set; } = "";
        public string LastName { get; set; } = "";
        public string NickName { get; set; } = "";
        public string QrCode { get; set; } = "";
        public string Matricula { get; set; } = "";
        public int UserId { get; set; }
        public bool Status { get; set; }
        public string Created { get; set; } = "";
        public string? EmailPessoal { get; set; }
        [NotMapped]
        public int ProjectId { get; set; }
        [NotMapped]
        public int FuncaoId { get; set; }
    }
}
