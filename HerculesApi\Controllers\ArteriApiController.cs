﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Net.Http;
using System.Threading.Tasks;

namespace HerculesApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class ArteriApiController : ControllerBase
    {
        private readonly IHttpClientFactory _httpClientFactory;

        public ArteriApiController(IHttpClientFactory httpClientFactory)
        {
            _httpClientFactory = httpClientFactory;
        }

        [HttpPost("consumir-arteri-api")]
        public async Task<IActionResult> ConsumirArteriApi()
        {
            var client = _httpClientFactory.CreateClient();

            // URL fornecida da ArteriApi
            var url = "https://smc.arteri.com.br/api/ws_baramaia";

            // Form data (substitua pelos campos e valores corretos)
            var formData = new MultipartFormDataContent();
            formData.Add(new StringContent("ODQ0OTc4NjVkOGQ3Mzg2ZDBmMw=="), "token");
            formData.Add(new StringContent("json_modulo_instalado"), "codigo_relatorio");
            //formData.Add(new StringContent("json_btx_btzero"), "codigo_relatorio");
            formData.Add(new StringContent("buscar_dados"), "acao");
            // Adicione mais campos conforme necessário...
            Console.WriteLine("teste");
            // Fazendo a requisição POST para a ArteriApi
            HttpResponseMessage response = await client.PostAsync(url, formData);

            // Verificando o status da resposta
            if (response.IsSuccessStatusCode)
            {
                var resultado = await response.Content.ReadAsStringAsync();
                Console.WriteLine(resultado.ToString());
                return Ok("Consegui acessar!");
                //return Ok(new { sucesso = true, dados = resultado });
            }
            else
            {
                Console.WriteLine("Erro ao consumir ArteriApi.");
                return StatusCode((int)response.StatusCode, new { sucesso = false, mensagem = "Erro ao consumir ArteriApi." });
            }
        }
    }
}
