﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using HerculesApi.Database;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using HerculesApi.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Microsoft.AspNetCore.Authorization;
using Microsoft.IdentityModel.Tokens;
using DocumentFormat.OpenXml.InkML;

namespace HerculesApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class VisitasController : ControllerBase
    {
        private readonly HerculesDbContext _context;
        private readonly AdmDbContext _contextAdm;
        public VisitasController(HerculesDbContext context, AdmDbContext contextAdm)
        {
            _context = context;
            _contextAdm = contextAdm;
        }
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Visita>>> GetVisitas()
        {
            // Busca todas as visitas (incluindo as de avulsos)
            var visitas = _context.Visitas.AsNoTracking().ToList();
            
            if (visitas.Count > 0)
            {
                foreach (var visita in visitas)
                {
                    // Verifica se é uma visita de avulso
                    var relAvulso = _context.RelVisitasAvulsos.AsNoTracking().Where(x => x.visId == visita.id).FirstOrDefault();
                    if (relAvulso != null)
                    {
                        visita.avulsoId = relAvulso.avulsoId;
                    }

                    var rel = _context.RelVisitasEquipamentos.AsNoTracking().Where(x => x.visId == visita.id).ToList();
                    if (rel.Count > 0)
                    {
                        var equipamentos = _context.EquipamentosVisitas.AsNoTracking().ToList();
                        List<EquipamentoVisita> listaInstalados = new List<EquipamentoVisita>();
                        List<EquipamentoVisita> listaRetirados = new List<EquipamentoVisita>();
                        foreach (var item in rel)
                        {
                            if (item.situacao == true)
                            {
                                listaInstalados.Add(equipamentos.Where(x => x.id == item.eqpId).FirstOrDefault());
                            }
                            else
                            {
                                listaRetirados.Add(equipamentos.Where(x => x.id == item.eqpId).FirstOrDefault());
                            }
                        }
                        visita.eqpInstalados = listaInstalados;
                        visita.eqpRetirados = listaRetirados;
                    }
                    var relfoto = _context.RelVisitasFotos.AsNoTracking().Where(x => x.visId == visita.id).Select(x => x.fotoId).ToList();
                    if (relfoto.Count > 0)
                    {
                        var fotos = _context.Fotos.AsNoTracking().Where(x => relfoto.Contains(x.id)).ToList();
                        visita.fotos = fotos;
                    }
                    var relMotivos = _context.RelVisitasMotivos.AsNoTracking().Where(x => x.visId == visita.id).Select(x => x.motId).ToList();
                    if (relMotivos.Count > 0)
                    {
                        var motivo = _context.Motivos.AsNoTracking().Where(x => relMotivos.Contains(x.id)).FirstOrDefault();
                        visita.motivo = motivo;
                    }
                    var relOrdens = _context.RelVisitasOrdens.AsNoTracking().Where(x => x.visId == visita.id).Select(x => x.ordemId).ToList();
                    if (relOrdens.Count > 0)
                    {
                        var ordens = _context.OrdensServico
                            .AsNoTracking()
                            .Where(x => relOrdens.Contains(x.id))
                            .Select(x => (int)x.id) // Conversão para int? aqui
                            .ToList();
                        visita.ordens = ordens;
                    }
                }

                return visitas;
            }
            else
            {
                return NotFound("Não há visitas");
            }
        }
        [HttpGet("{id}")]
        public async Task<ActionResult<Visita>> GetVisita(int id)
        {
            var visita = await _context.Visitas.FindAsync(id);
            if (visita == null)
            {
                return NotFound("Visita não encontrada");
            }

            // Verifica se é uma visita de avulso
            var relAvulso = _context.RelVisitasAvulsos.AsNoTracking().Where(x => x.visId == visita.id).FirstOrDefault();
            if (relAvulso != null)
            {
                visita.avulsoId = relAvulso.avulsoId;
            }

            var rel = _context.RelVisitasEquipamentos.AsNoTracking().Where(x => x.visId == visita.id).ToList();
            if (rel.Count > 0)
            {
                var equipamentos = _context.EquipamentosVisitas.AsNoTracking().ToList();
                List<EquipamentoVisita> listaInstalados = new List<EquipamentoVisita>();
                List<EquipamentoVisita> listaRetirados = new List<EquipamentoVisita>();
                foreach (var item in rel)
                {
                    if (item.situacao == true)
                    {
                        listaInstalados.Add(equipamentos.Where(x => x.id == item.eqpId).FirstOrDefault());
                    }
                    else
                    {
                        listaRetirados.Add(equipamentos.Where(x => x.id == item.eqpId).FirstOrDefault());
                    }
                }
                visita.eqpInstalados = listaInstalados;
                visita.eqpRetirados = listaRetirados;
            }
            var relmotivo = _context.RelVisitasMotivos.AsNoTracking().Where(x => x.visId == visita.id).Select(x => x.motId).ToList();
            if (relmotivo.Count > 0)
            {
                var motivo = _context.Motivos.AsNoTracking().Where(x => relmotivo.Contains(x.id)).FirstOrDefault();
                visita.motivo = motivo;
            }
            var relfoto = _context.RelVisitasFotos.AsNoTracking().Where(x => x.visId == visita.id).Select(x => x.fotoId).ToList();
            if (relfoto.Count > 0)
            {
                var fotos = _context.Fotos.AsNoTracking().Where(x => relfoto.Contains(x.id)).ToList();
                visita.fotos = fotos;
            }
            var relOrden = _context.RelVisitasOrdens.AsNoTracking().Where(x => x.visId == visita.id).Select(x => x.ordemId).ToList();
            if (relOrden.Count > 0)
            {
                var ordens = _context.OrdensServico
                    .AsNoTracking()
                    .Where(x => relOrden.Contains(x.id))
                    .Select(x => (int)x.id) // Conversão para int? aqui
                    .ToList();
                visita.ordens = ordens;
            }

            return visita;
        }
        [HttpGet("completa/{id}")]
        public async Task<ActionResult<VisitaDTO>> GetVisitaCompleta(int id)
        {
            VisitaDTO visitaCompleta = new VisitaDTO();
            var visita = await _context.Visitas.FindAsync(id);
            
            // Verifica se é uma visita de avulso
            var relAvulso = _context.RelVisitasAvulsos.AsNoTracking().Where(x => x.visId == visita.id).FirstOrDefault();
            if (relAvulso != null)
            {
                visita.avulsoId = relAvulso.avulsoId;
                
                // Busca os dados completos do avulso
                var avulsoCompleto = _context.vw_avulsos.AsNoTracking().Where(x => x.id == relAvulso.avulsoId).FirstOrDefault();
                if (avulsoCompleto != null)
                {
                    visitaCompleta.avulso = avulsoCompleto;
                }
            }
            
            visitaCompleta.visita = visita;
            var rel = _context.RelVisitasEquipamentos.AsNoTracking().Where(x => x.visId == visita.id).ToList();
            if (rel.Count > 0)
            {
                var equipamentos = _context.EquipamentosVisitas.AsNoTracking().ToList();
                List<EquipamentoVisita> listaInstalados = new List<EquipamentoVisita>();
                List<EquipamentoVisita> listaRetirados = new List<EquipamentoVisita>();
                foreach (var item in rel)
                {
                    if (item.situacao == true)
                    {
                        listaInstalados.Add(equipamentos.Where(x => x.id == item.eqpId).FirstOrDefault());
                    }
                    else
                    {
                        listaRetirados.Add(equipamentos.Where(x => x.id == item.eqpId).FirstOrDefault());
                    }
                }
                if (!listaInstalados.IsNullOrEmpty())
                {
                    foreach (var item in listaInstalados)
                    {
                        var relFoto = _context.RelEquipVisitaFotos.Where(x => x.visitaId == visita.id && x.equipId == item.id).FirstOrDefault();
                        var foto = _context.Fotos.Where(x => x.id == relFoto.fotoId).FirstOrDefault();
                        item.foto = foto;
                    }
                    visita.eqpInstalados = listaInstalados;
                }
                if (!listaRetirados.IsNullOrEmpty())
                {
                    foreach (var item in listaRetirados)
                    {
                        var relFoto = _context.RelEquipVisitaFotos.Where(x => x.visitaId == visita.id && x.equipId == item.id).FirstOrDefault();
                        var foto = _context.Fotos.Where(x => x.id == relFoto.fotoId).FirstOrDefault();
                        item.foto = foto;
                    }
                    visita.eqpRetirados = listaRetirados;
                }
            }
            var relmotivo = _context.RelVisitasMotivos.AsNoTracking().Where(x => x.visId == visita.id).Select(x => x.motId).ToList();
            if (relmotivo.Count > 0)
            {
                var motivo = _context.Motivos.AsNoTracking().Where(x => relmotivo.Contains(x.id)).FirstOrDefault();
                visita.motivo = motivo;
            }
            var relfoto = _context.RelVisitasFotos.AsNoTracking().Where(x => x.visId == visita.id).Select(x => x.fotoId).ToList();
            if (relfoto.Count > 0)
            {
                var fotos = _context.Fotos.AsNoTracking().Where(x => relfoto.Contains(x.id)).ToList();
                visita.fotos = fotos;
            }
            var relOrden = _context.RelVisitasOrdens.AsNoTracking().Where(x => x.visId == visita.id).Select(x => x.ordemId).ToList();
            if (relOrden.Count > 0)
            {
                var ordens = _context.OrdensServico
                    .AsNoTracking()
                    .Where(x => relOrden.Contains(x.id))
                    .ToList();
                foreach (var item in ordens)
                {
                    var users = await _context.RelOrdensTecnicos
                            .AsNoTracking()
                            .Where(x => x.ordemId == item.id)
                            .Select(x => x.tecId)
                            .ToListAsync();
                    item.userId.AddRange(users);

                    if (users.Count > 0)
                    {
                        var userIds = users.ToHashSet(); // Evita duplicados
                        var usuarios = await _contextAdm.Users
                            .AsNoTracking()
                            .Where(u => userIds.Contains(u.UserId))
                            .ToListAsync();

                        // Constrói a string de técnicos
                        item.tecnicos = string.Join(" - ", usuarios.Select(u => $"{u.FirstName} {u.LastName}"));
                    }
                }
                visitaCompleta.ordens = ordens;
            }

            return visitaCompleta;
        }

        [HttpGet("teste-avulso/{id}")]
        public async Task<ActionResult<object>> TesteAvulso(int id)
        {
            // Endpoint de teste para verificar se os dados do avulso estão sendo retornados
            var visita = await _context.Visitas.FindAsync(id);
            if (visita == null)
            {
                return NotFound("Visita não encontrada");
            }

            var relAvulso = _context.RelVisitasAvulsos.AsNoTracking().Where(x => x.visId == visita.id).FirstOrDefault();
            if (relAvulso == null)
            {
                return Ok(new { 
                    message = "Esta visita não é um avulso",
                    visitaId = id,
                    cliId = "Visita normal com ordens"
                });
            }

            var avulsoCompleto = _context.vw_avulsos.AsNoTracking().Where(x => x.id == relAvulso.avulsoId).FirstOrDefault();
            
            return Ok(new {
                message = "Dados do avulso encontrados",
                visitaId = id,
                relAvulso = relAvulso,
                avulsoCompleto = avulsoCompleto
            });
        }

        [HttpPost]
        public async Task<ActionResult<Visita>> PostVisita(Visita visita)
        {
            if (visita == null) return BadRequest("Visita inválida");

            // Correção para evitar NullPointerException no Android
            visita.vis_descricao = visita.vis_descricao ?? "";
            visita.vis_problemas = visita.vis_problemas ?? "";
            visita.vis_observacao = visita.vis_observacao ?? "";
            visita.vis_gps = visita.vis_gps ?? "";


            List<RelVisitaMotivo> listaVisMot = new List<RelVisitaMotivo>();
            List<RelVisitaFoto> listaFotos = new List<RelVisitaFoto>();
            List<RelVisitaOrdem> listaOdens = new List<RelVisitaOrdem>();
            int ordId = 0;
            Empreiteira empreiteira = new Empreiteira();

            if (visita != null)
            {
                try
                {
                    _context.Visitas.Add(visita);
                    await _context.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    return BadRequest(new { 
                        error = "Erro ao salvar visita no banco de dados", 
                        details = ex.Message,
                        innerException = ex.InnerException?.Message
                    });
                }
            }

            if (visita.vis_ordId > 0)
            {
                RelVisitaOrdem r1 = new RelVisitaOrdem();
                r1.ordemId = Convert.ToInt32(visita.vis_ordId);
                r1.visId = visita.id;
                _context.RelVisitasOrdens.Add(r1);
                await _context.SaveChangesAsync();

                if (!visita.ordens.IsNullOrEmpty())
                {
                    var idordens = visita.ordens.ToList();
                    foreach (var item in idordens)
                    {
                        RelVisitaOrdem relordem = new RelVisitaOrdem
                        {
                            visId = visita.id,
                            ordemId = item
                        };
                        listaOdens.Add(relordem);
                        ordId = item;

                        var ord = _context.OrdensServico.Where(x => x.id == item).FirstOrDefault();
                        ord.ord_status = 1;
                        ord.ord_dataFechamento = visita.vis_data;
                        _context.OrdensServico.Update(ord);
                        await _context.SaveChangesAsync();

                    }
                    _context.RelVisitasOrdens.AddRange(listaOdens);
                    await _context.SaveChangesAsync();

                }

                var ordem = _context.OrdensServico.Where(x => x.id == visita.vis_ordId).FirstOrDefault();
                ordem.ord_status = 1;
                ordem.ord_dataFechamento = visita.vis_data;
                _context.OrdensServico.Update(ordem);
                await _context.SaveChangesAsync();

                if (ordem.ord_na > 0)
                {
                    var ouvidoria = _context.Ouvidorias.Where(x => x.ouv_na == ordem.ord_na).FirstOrDefault();
                    ouvidoria.ouv_status = 1;
                }

                if (visita.vis_efetivado == 1)
                {
                    empreiteira = _context.Empreiteiras.Where(x => x.emp_desc.Equals(ordem.ord_empreiteira)).FirstOrDefault();
                }

                if (!visita.fotos.IsNullOrEmpty())
                {
                    foreach (var foto in visita.fotos)
                    {
                        if (!string.IsNullOrEmpty(foto.foto_dado_base64))
                        {
                            foto.foto_dado = Convert.FromBase64String(foto.foto_dado_base64);
                        }
                        _context.Fotos.Add(foto);
                        await _context.SaveChangesAsync();

                        RelVisitaFoto relfoto = new RelVisitaFoto();
                        relfoto.visId = visita.id;
                        relfoto.fotoId = foto.id;
                        listaFotos.Add(relfoto);

                        _context.RelVisitasFotos.Add(relfoto);
                        await _context.SaveChangesAsync();

                    }
                }
                if (visita.motivo != null)
                {
                    RelVisitaMotivo relmotivo = new RelVisitaMotivo();
                    relmotivo.visId = visita.id;
                    relmotivo.motId = visita.motivo.id;

                    _context.RelVisitasMotivos.Add(relmotivo);
                    await _context.SaveChangesAsync();

                    if (visita.motivo.mot_acao == 1)
                    {
                        Adequacao adequacao = new Adequacao
                        {
                            ade_cliId = Convert.ToInt32(ordem.ord_cliId),
                            ade_cp = Convert.ToInt32(ordem.ord_cp),
                            ade_data = Convert.ToString(DateOnly.FromDateTime(DateTime.Now)),
                            ade_empreiteira = ordem.ord_empreiteira,
                            ade_situacao = ordem.ord_status_uc,
                            ade_tecnicos = ordem.ord_tecnicos,
                            ade_motivo = "0.01 " + visita.motivo.mot_descricao,
                            ade_obs = visita.vis_observacao,
                        };

                        _context.Adequacoes.Add(adequacao);
                        await _context.SaveChangesAsync();
                    }
                    if (visita.motivo.mot_acao == 2)
                    {
                        string datas = "";
                        var (ordens, temDiferenca30Dias, diasDiferenca) = GetOrdensClienteInternal(Convert.ToInt32(ordem.ord_cliId));                        
                        if (temDiferenca30Dias==true)
                        {
                            foreach (var item in ordens)
                            {
                                datas = datas + " - " + DateOnly.FromDateTime(Convert.ToDateTime(item.ord_dataEntrada));
                            }
                            Adequacao adequacao = new Adequacao
                            {
                                ade_cliId = Convert.ToInt32(ordem.ord_cliId),
                                ade_cp = Convert.ToInt32(ordem.ord_cp),
                                ade_data = Convert.ToString(DateOnly.FromDateTime(DateTime.Now)),
                                ade_empreiteira = ordem.ord_empreiteira,
                                ade_situacao = ordem.ord_status_uc,
                                ade_tecnicos = ordem.ord_tecnicos,
                                ade_motivo = "0.01 "+"Cliente ausente em múltiplas visitas: " + datas,
                                ade_obs = visita.vis_observacao,
                            };

                            _context.Adequacoes.Add(adequacao);
                            await _context.SaveChangesAsync();
                        }
                    }
                }
            }

            if (visita.vis_ordId == 0)
            {
                RelVisitaAvulso relAvulso = new RelVisitaAvulso();
                relAvulso.visId = visita.id;
                relAvulso.avulsoId = Convert.ToInt32(visita.avulsoId);

                _context.RelVisitasAvulsos.Add(relAvulso);
                await _context.SaveChangesAsync();

                if (!visita.fotos.IsNullOrEmpty())
                {
                    foreach (var foto in visita.fotos)
                    {
                        if (!string.IsNullOrEmpty(foto.foto_dado_base64))
                        {
                            foto.foto_dado = Convert.FromBase64String(foto.foto_dado_base64);
                        }
                        _context.Fotos.Add(foto);
                        await _context.SaveChangesAsync();

                        RelVisitaFoto relfoto = new RelVisitaFoto();
                        relfoto.visId = visita.id;
                        relfoto.fotoId = foto.id;
                        listaFotos.Add(relfoto);

                        _context.RelVisitasFotos.Add(relfoto);
                        await _context.SaveChangesAsync();

                    }
                }

            }

            return Ok(visita);
        }

        [HttpPost("validate")]
        public ActionResult<object> ValidateVisita(Visita visita)
        {
            if (visita == null)
            {
                return BadRequest(new { error = "Visita é null", details = "O objeto visita não foi enviado ou está vazio" });
            }

            var validationErrors = new List<string>();

            // Verificar campos obrigatórios
            if (visita.vis_data == default(DateTime))
            {
                validationErrors.Add("vis_data está com valor padrão ou inválido");
            }

            if (string.IsNullOrEmpty(visita.vis_descricao))
            {
                validationErrors.Add("vis_descricao está null ou vazio");
            }

            if (visita.vis_efetivado < 0 || visita.vis_efetivado > 1)
            {
                validationErrors.Add($"vis_efetivado tem valor inválido: {visita.vis_efetivado}");
            }

            // Aplicar sanitização simples
            visita.vis_descricao = visita.vis_descricao ?? "";
            visita.vis_problemas = visita.vis_problemas ?? "";
            visita.vis_observacao = visita.vis_observacao ?? "";
            visita.vis_gps = visita.vis_gps ?? "";

            return Ok(new
            {
                isValid = validationErrors.Count == 0,
                errors = validationErrors,
                sanitizedVisita = new
                {
                    vis_data = visita.vis_data,
                    vis_efetivado = visita.vis_efetivado,
                    vis_descricao = visita.vis_descricao,
                    vis_problemas = visita.vis_problemas,
                    vis_observacao = visita.vis_observacao,
                    vis_gps = visita.vis_gps,
                    vis_status = visita.vis_status,
                    vis_ordId = visita.vis_ordId,
                    vis_eqpVisitaId = visita.vis_eqpVisitaId
                }
            });
        }

        [HttpPost("debug")]
        public ActionResult<object> DebugVisita([FromBody] object rawData)
        {
            try
            {
                var jsonString = rawData?.ToString() ?? "null";
                
                return Ok(new
                {
                    success = true,
                    receivedData = rawData,
                    dataType = rawData?.GetType().Name ?? "null",
                    jsonString = jsonString,
                    timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    error = "Erro ao processar dados de debug",
                    message = ex.Message,
                    innerException = ex.InnerException?.Message,
                    timestamp = DateTime.UtcNow
                });
            }
        }
        //[HttpGet("clienteId")]
        //public List<OrdemServico> GetOrdensCliente(int cliId)
        //{
        //    var dataLimite = DateTime.Now.AddDays(-90);


        //        var ordens = _context.OrdensServico
        //            .Where(o => o.ord_cliId == cliId)
        //            .Join(
        //                _context.RelVisitasOrdens,
        //                ordem => ordem.id,
        //                rel => rel.ordemId,
        //                (ordem, rel) => new { Ordem = ordem, Rel = rel }
        //            )
        //            .Join(
        //                _context.Visitas,
        //                combined => combined.Rel.visId,
        //                visita => visita.id,
        //                (combined, visita) => new { combined.Ordem, Visita = visita }
        //            )
        //            .Where(x => x.Visita.vis_efetivado == 0 &&
        //                        x.Visita.vis_data >= dataLimite)
        //            .Join(
        //                _context.RelVisitasMotivos,
        //                combined => combined.Visita.id,
        //                relMotivo => relMotivo.visId,
        //                (combined, relMotivo) => new { combined.Ordem, combined.Visita, RelMotivo = relMotivo }
        //            )
        //            .Where(x => x.RelMotivo.motId == 1)
        //            .Select(x => x.Ordem)
        //            .Distinct() // Para evitar duplicatas caso uma ordem tenha múltiplas visitas que atendam os critérios
        //            .ToList();

        //        return ordens;

        //}

        [HttpGet("clienteId")]
        public IActionResult GetOrdensCliente(int cliId)
        {
            var (ordens, temDiferenca30Dias, diasDiferenca) = GetOrdensClienteInternal(cliId);

            return Ok(new
            {
                Ordens = ordens,
                TemDiferenca30Dias = temDiferenca30Dias,
                DiasDiferenca = diasDiferenca
            });
        }
        private (List<OrdemServico> ordens, bool temDiferenca30Dias, int? diasDiferenca) GetOrdensClienteInternal(int cliId)
        {
            var dataLimite = DateTime.Now.AddDays(-90);

            var listaOrdens = _context.OrdensServico
            .Where(o => o.ord_cliId == cliId)
            .Join(
                _context.RelVisitasOrdens,
                ordem => ordem.id,
                rel => rel.ordemId,
                (ordem, rel) => new { Ordem = ordem, Rel = rel }
            )
            .Join(
                _context.Visitas,
                combined => combined.Rel.visId,
                visita => visita.id,
                (combined, visita) => new { combined.Ordem, Visita = visita }
            )
            .Where(x => x.Visita.vis_efetivado == 0 &&
                        x.Visita.vis_data >= dataLimite)
            .Join(
                _context.RelVisitasMotivos,
                combined => combined.Visita.id,
                relMotivo => relMotivo.visId,
                (combined, relMotivo) => new { combined.Ordem, combined.Visita, RelMotivo = relMotivo }
            )
            .Where(x => x.RelMotivo.motId == 1)
            .Select(x => x.Ordem)
            .Distinct()
            .OrderBy(o => o.ord_dataEntrada) // Ordenar por data para pegar a primeira e última
            .ToList();

            // Verificar a diferença de dias
            bool temDiferenca = false;
            int? diasDiferenca = null;

            if (listaOrdens.Count >= 2)
            {
                var primeiraOrdem = listaOrdens.First();
                var ultimaOrdem = listaOrdens.Last();

                if (primeiraOrdem.ord_dataEntrada.HasValue && ultimaOrdem.ord_dataEntrada.HasValue)
                {
                    diasDiferenca = (ultimaOrdem.ord_dataEntrada.Value - primeiraOrdem.ord_dataEntrada.Value).Days;
                    temDiferenca = diasDiferenca >= 30;
                }
            }

            return (listaOrdens, temDiferenca, diasDiferenca);

        }

        [HttpPut("{id}")]
        public async Task<IActionResult> PutVisita(int id, Visita visita)
        {
            var vis = _context.Visitas.Where(x => x.id == id).AsNoTracking().FirstOrDefault();
            if (vis == null)
            {
                return BadRequest("Visita não encontrada");
            }

            _context.Entry(visita).State = EntityState.Modified;
            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!VisitaExists(id))
                {
                    return NotFound("Visita não encontrada");
                }
                else
                {
                    throw;
                }
            }

            return Ok("Visita alterada com sucesso!");
        }
        //[HttpDelete("{id}")]
        //public async Task<IActionResult> DeleteVisita(int id)
        //{
        //    List<OrdemServico>ordens = new List<OrdemServico>();
        //    var visita = await _context.Visitas.FindAsync(id);
        //    if (visita == null)
        //    {
        //        return NotFound("Visita não encontrada");
        //    }
        //    else
        //    {

        //        var relEquipamentos = _context.RelVisitasEquipamentos.Where(x => x.visId == id).ToList();
        //        if (relEquipamentos!= null)
        //        {
        //            _context.RelVisitasEquipamentos.RemoveRange(relEquipamentos);
        //            await _context.SaveChangesAsync();
        //        }
        //        //excluir também o equipamento da visita EquipamentosVisitas
        //        //excluir também o relacionamento de equipamento da visita fotos RelEquipVisitaFotos
        //        var relFotos = _context.RelVisitasFotos.Where(x => x.visId == id).ToList();
        //        if (relFotos != null)
        //        {
        //            _context.RelVisitasFotos.RemoveRange(relFotos);
        //            await _context.SaveChangesAsync();
        //        }
        //        var relMotivos = _context.RelVisitasMotivos.Where(x => x.visId == id).ToList();
        //        if (relMotivos != null)
        //        {
        //            _context.RelVisitasMotivos.RemoveRange(relMotivos);
        //            await _context.SaveChangesAsync();
        //        }
        //        var relOrdens = _context.RelVisitasOrdens.AsNoTracking().Where(x => x.visId == id).Select(x=>x.ordemId).ToList();
        //        var relO = _context.RelVisitasOrdens.AsNoTracking().Where(x => x.visId == id).ToList();
        //        foreach (var item in relOrdens)
        //        {
        //            var ordem = _context.OrdensServico.AsNoTracking().Where(x => x.id == item).FirstOrDefault();
        //            ordem.ord_status = 2;
        //            ordens.Add(ordem);
        //        }
        //        _context.OrdensServico.UpdateRange(ordens);
        //        await _context.SaveChangesAsync();
        //        if (relO != null)
        //        {
        //            _context.RelVisitasOrdens.RemoveRange(relO);
        //            await _context.SaveChangesAsync();
        //        }


        //        //visita.vis_status= false;
        //        //_context.Visitas.Update(visita);
        //        //await _context.SaveChangesAsync();
        //        _context.Visitas.Remove(visita);
        //        await _context.SaveChangesAsync();

        //        return Ok("Visita excluida com sucesso!");
        //    }

        //}
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteVisita(int id)
        {
            List<OrdemServico> ordens = new List<OrdemServico>();
            var visita = await _context.Visitas.FindAsync(id);
            if (visita == null)
            {
                return NotFound("Visita não encontrada");
            }
            else
            {
                var relEquipamentos = _context.RelVisitasEquipamentos.Where(x => x.visId == id).ToList();
                //excluir também o equipamento da visita EquipamentosVisitas
                //excluir também o relacionamento de equipamento da visita fotos RelEquipVisitaFotos
                var relFotos = _context.RelVisitasFotos.Where(x => x.visId == id).ToList();
                var relMotivos = _context.RelVisitasMotivos.Where(x => x.visId == id).ToList();
                var relOrdens = _context.RelVisitasOrdens.AsNoTracking().Where(x => x.visId == id).Select(x => x.ordemId).ToList();
                foreach (var item in relOrdens)
                {
                    var ordem = _context.OrdensServico.AsNoTracking().Where(x => x.id == item).FirstOrDefault();
                    //ordem.ord_status = 2;
                    ordens.Add(ordem);
                }
                _context.OrdensServico.UpdateRange(ordens);
                await _context.SaveChangesAsync();
                //_context.OrdensServico.RemoveRange(ordens);
                //await _context.SaveChangesAsync();

                visita.vis_status = false;
                _context.Visitas.Update(visita);
                await _context.SaveChangesAsync();
                //_context.Visitas.Remove(visita);
                //await _context.SaveChangesAsync();

                return Ok("Visita excluida com sucesso!");
            }

        }
        private bool VisitaExists(int id)
        {
            return _context.Visitas.Any(e => e.id == id);
        }


        [HttpGet("teste-incluir-avulsos")]
        public async Task<ActionResult> TesteIncluirAvulsos()
        {
            try
            {
                // Busca visitas normais (com ordens)
                var visitasComOrdens = await _context.Visitas
                    .Where(v => v.vis_ordId > 0)
                    .CountAsync();

                // Busca visitas de avulsos
                var visitasDeAvulsos = await (from v in _context.Visitas
                                             join ra in _context.RelVisitasAvulsos on v.id equals ra.visId
                                             select v).CountAsync();

                // Busca visitas sem ordem e sem avulso (órfãs)
                var visitasOrfas = await _context.Visitas
                    .Where(v => (v.vis_ordId == 0 || v.vis_ordId == null) && 
                               !_context.RelVisitasAvulsos.Any(ra => ra.visId == v.id))
                    .CountAsync();

                // Exemplo de visitas de avulsos
                var exemploVisitasAvulsos = await (from v in _context.Visitas
                                                  join ra in _context.RelVisitasAvulsos on v.id equals ra.visId
                                                  join a in _context.Avulsos on ra.avulsoId equals a.id
                                                  select new
                                                  {
                                                      VisitaId = v.id,
                                                      VisitaData = v.vis_data,
                                                      AvulsoId = a.id,
                                                      AvulsoNome = a.avu_nome,
                                                      Efetivado = v.vis_efetivado
                                                  }).Take(5).ToListAsync();

                return Ok(new
                {
                    VisitasComOrdens = visitasComOrdens,
                    VisitasDeAvulsos = visitasDeAvulsos,
                    VisitasOrfas = visitasOrfas,
                    TotalVisitas = visitasComOrdens + visitasDeAvulsos + visitasOrfas,
                    ExemplosVisitasAvulsos = exemploVisitasAvulsos,
                    Mensagem = "Agora as visitas de avulsos devem aparecer junto com as visitas normais"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    Erro = "Erro ao buscar dados de teste",
                    Detalhes = ex.Message,
                    InnerException = ex.InnerException?.Message
                });
            }
        }

        [HttpGet("avulsos-disponiveis")]
        public async Task<ActionResult> GetAvulsosDisponiveis()
        {
            try
            {
                // Busca todos os avulsos que ainda não têm visitas associadas ou que têm visitas não efetivadas
                var avulsosDisponiveis = await (from a in _context.Avulsos
                                               where !_context.RelVisitasAvulsos.Any(ra => ra.avulsoId == a.id) ||
                                                     _context.RelVisitasAvulsos.Any(ra => ra.avulsoId == a.id &&
                                                     _context.Visitas.Any(v => v.id == ra.visId && v.vis_efetivado == 0))
                                               select new
                                               {
                                                   id = a.id,
                                                   nome = a.avu_nome,
                                                   logradouro = a.avu_logradouro,
                                                   numero = a.avu_numero,
                                                   complemento = a.avu_complemento,
                                                   uc = a.avu_uc,
                                                   cp = a.avu_cp,
                                                   cs = a.avu_cs,
                                                   modulo = a.avu_modulo,
                                                   display = a.avu_display,
                                                   tecnico = a.avu_tecnico
                                               }).ToListAsync();

                return Ok(new
                {
                    avulsos = avulsosDisponiveis,
                    total = avulsosDisponiveis.Count,
                    mensagem = $"Encontrados {avulsosDisponiveis.Count} avulsos disponíveis para visitas"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    erro = "Erro ao buscar avulsos disponíveis",
                    detalhes = ex.Message
                });
            }
        }
    }
}