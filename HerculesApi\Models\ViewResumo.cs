﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HerculesApi.Models
{
    public class ViewResumo
    {
        [Key]
        public int id { get; set; }
        public DateTime data { get; set; }
        public int efetivado { get; set; }
        public string tipoServico { get; set; }
        public int tecnicoId { get; set; }
        public string motivoNaoEfetivado { get; set; }
    }
}
