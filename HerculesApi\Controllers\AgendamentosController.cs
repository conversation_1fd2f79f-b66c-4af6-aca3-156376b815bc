﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using HerculesApi.Database;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using HerculesApi.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Microsoft.AspNetCore.Authorization;

namespace HerculesApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class AgendamentosController : ControllerBase
    {
        private readonly HerculesDbContext _context;
        public AgendamentosController(HerculesDbContext context)
        {
            _context = context;
        }
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Agendamento>>> GetAgendamentos()
        {
            var agendamentos = await _context.Agendamentos.ToListAsync();

            if (agendamentos.Count > 0)
            {
                foreach (var item in agendamentos)
                {
                    // Garantir que as listas não são nulas
                    if (item.userIds == null)
                        item.userIds = new List<int>();

                    if (item.tpServIds == null)
                        item.tpServIds = new List<int>();

                    var users = await _context.RelAgendTecnicos
                        .AsNoTracking()
                        .Where(x => x.agdId == item.id)
                        .Select(x => x.userId)
                        .ToListAsync();

                    item.userIds.AddRange(users);

                    var tipos = await _context.RelAgendTipoServicos
                        .AsNoTracking()
                        .Where(x => x.agdId == item.id)
                        .Select(x => x.tpServId)
                        .ToListAsync();

                    item.tpServIds.AddRange(tipos);
                }
                return agendamentos;
            }
            else
            {
                return NotFound("Não há agendamentos");
            }
        }


        [HttpGet("{id}")]
        public async Task<ActionResult<Agendamento>> GetAgendamento(int id)
        {
            Agendamento agendamento = _context.Agendamentos.AsNoTracking().Where(x=>x.id==id).FirstOrDefault();
            var listaTecnicos = _context.RelAgendTecnicos.AsNoTracking().Where(x => x.agdId == id).ToList();
            var listaServicos = _context.RelAgendTipoServicos.AsNoTracking().Where(x => x.agdId == id).ToList();
            if (agendamento == null)
            {
                return NotFound("Agendamento não encontrado");
            }
            else
            {
                if (listaTecnicos.Count > 0)
                {
                    foreach (var tec in listaTecnicos)
                    {
                        agendamento.userIds.Add(tec.userId);
                    }
                }
                if (listaServicos.Count > 0)
                {
                    foreach (var serv in listaServicos)
                    {
                        agendamento.tpServIds.Add(serv.tpServId);
                    }
                }
                return agendamento;
            }
        }
        [HttpPost]
        public async Task<ActionResult<Agendamento>> PostAgendamento(Agendamento agendamento)
        {
            agendamento.agd_cliNome.ToUpper();
            List<RelAgendaTecnico> listaTecnicos = new List<RelAgendaTecnico>();
            List<RelAgendaTipoServico> listaTipoServicos = new List<RelAgendaTipoServico>();

            var agend = _context.Agendamentos
                .Where(x => x.agd_cliId.Equals(agendamento.agd_cliId))
                .AsNoTracking()
                .FirstOrDefault();
            if (agend != null)
            {
                return BadRequest(new { message = "Não é possível cadastrar agendamento para cliente já agendado!" });
            }
            else
            {
                _context.Agendamentos.Add(agendamento);
                await _context.SaveChangesAsync();

                foreach (var item in agendamento.userIds)
                {
                    RelAgendaTecnico relAgendaTecnico = new RelAgendaTecnico();
                    relAgendaTecnico.agdId = agendamento.id;
                    relAgendaTecnico.userId = item;
                    listaTecnicos.Add(relAgendaTecnico);
                }
                _context.RelAgendTecnicos.AddRange(listaTecnicos);
                await _context.SaveChangesAsync();

                foreach (var rel in agendamento.tpServIds)
                {
                    RelAgendaTipoServico relAgendaServico = new RelAgendaTipoServico();
                    relAgendaServico.agdId = agendamento.id;
                    relAgendaServico.tpServId = rel;
                    listaTipoServicos.Add(relAgendaServico);
                }
                _context.RelAgendTipoServicos.AddRange(listaTipoServicos);
                await _context.SaveChangesAsync();

                
            ///************* Atualização de adequação ************//////////
                // Remove a adequação existente se houver agendamento para o mesmo cliente

                var adequacao = _context.Adequacoes.Where(x=>x.ade_cliId==agendamento.agd_cliId).FirstOrDefault();
                if (adequacao != null)
                {
                    _context.Adequacoes.Remove(adequacao);
                    await _context.SaveChangesAsync();
                }

                return Ok("Agendamento feito com sucesso!");
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> PutAgendamento(int id, Agendamento agendamento)
        {
            agendamento.agd_cliNome.ToUpper();
            _context.Entry(agendamento).State = EntityState.Modified;
            try
            {
                await _context.SaveChangesAsync();

                //////////************* Atualização de relacionamento dos técnicos ************//////////
                // Remove as relações de técnicos que não estão mais na nova lista (tecnicos a remover)
             var listaTecnicos = _context.RelAgendTecnicos
            .AsNoTracking()
            .Where(x => x.agdId == id)
            .Select(r => r.userId)
            .ToList();

                var listaServicos = _context.RelAgendTipoServicos
                    .AsNoTracking()
                    .Where(x => x.agdId == id)
                    .Select(r => r.tpServId)
                    .ToList();

                var tecnicosParaRemover = listaTecnicos
                    .Except(agendamento.userIds) // Técnicos antigos que não estão mais na lista nova
                    .ToList();

                if (tecnicosParaRemover.Any())
                {
                    var relacoesParaRemover = _context.RelAgendTecnicos.AsNoTracking()
                        .Where(r => r.agdId == agendamento.id && tecnicosParaRemover.Contains(r.userId))
                        .ToList();

                    _context.RelAgendTecnicos.RemoveRange(relacoesParaRemover);
                }

                // Adiciona as novas relações de técnicos que não existiam anteriormente
                var tecnicosParaAdicionar = agendamento.userIds
                    .Except(listaTecnicos) // Apenas os técnicos que não estavam previamente relacionados
                    .ToList();

                foreach (var userId in tecnicosParaAdicionar)
                {
                    var novaRelacao = new RelAgendaTecnico
                    {
                        agdId = agendamento.id,
                        userId = userId
                    };
                    _context.RelAgendTecnicos.Add(novaRelacao);
                }

                //////////************* Atualização de relacionamento dos serviços ************//////////
                // Remove as relações de serviços que não estão mais na nova lista
                var servicosParaRemover = listaServicos
                    .Except(agendamento.tpServIds)
                    .ToList();

                if (servicosParaRemover.Any())
                {
                    var relacoesServParaRemover = _context.RelAgendTipoServicos.AsNoTracking()
                        .Where(r => r.agdId == agendamento.id && servicosParaRemover.Contains(r.tpServId))
                        .ToList();

                    _context.RelAgendTipoServicos.RemoveRange(relacoesServParaRemover);
                }

                // Adiciona os novos serviços que não existiam anteriormente
                var servicosParaAdicionar = agendamento.tpServIds
                    .Except(listaServicos)
                    .ToList();

                foreach (var tpId in servicosParaAdicionar)
                {
                    var novaRelacao = new RelAgendaTipoServico
                    {
                        agdId = agendamento.id,
                        tpServId = tpId
                    };
                    _context.RelAgendTipoServicos.Add(novaRelacao);
                }

                // Salva as alterações na base de dados
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!AgendamentoExists(id))
                {
                    return NotFound("Agendamento não encontrado");
                }
                else
                {
                    throw;
                }
            }

            return Ok("Agendamento alterado com sucesso!");
        }


        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteAgendamento(int id)
        {
            var agendamento = await _context.Agendamentos.FindAsync(id);
            if (agendamento == null)
            {
                return NotFound("Agendamento não encontrado");
            }
            else
            {
                _context.Agendamentos.Remove(agendamento);
                await _context.SaveChangesAsync();


                var listaTecnicos = _context.RelAgendTecnicos.Where(x => x.agdId == agendamento.id).ToList();
                if (listaTecnicos.Count > 0)
                {
                    _context.RelAgendTecnicos.RemoveRange(listaTecnicos);
                    await _context.SaveChangesAsync();
                }
                var listaServicos = _context.RelAgendTipoServicos.Where(r => r.agdId == agendamento.id).ToList();
                if (listaServicos.Count > 0)
                {
                    _context.RelAgendTipoServicos.RemoveRange(listaServicos);
                    await _context.SaveChangesAsync();
                }

                return Ok("Agendamento excluido com sucesso!");
            }

        }
        private bool AgendamentoExists(int id)
        {
            return _context.Agendamentos.Any(e => e.id == id);
        }
    }
}