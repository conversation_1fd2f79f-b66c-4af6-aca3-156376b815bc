﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using HerculesApi.Database;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using HerculesApi.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Microsoft.AspNetCore.Authorization;

namespace HerculesApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class RelVisitaEquipamentosController : ControllerBase
    {
        private readonly HerculesDbContext _context;
        public RelVisitaEquipamentosController(HerculesDbContext context)
        {
            _context = context;
        }
        [HttpPost]
        public async Task<ActionResult<RelVisitaEquipamento>> PostRelVisitaEquipamento(List<RelVisitaEquipamento>  relVisitaEq)
        {
            _context.RelVisitasEquipamentos.AddRange(relVisitaEq);
            await _context.SaveChangesAsync();

            return Ok("Relacionamento salvo!");
        }
    }
}