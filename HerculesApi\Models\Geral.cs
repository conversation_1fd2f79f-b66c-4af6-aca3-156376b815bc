﻿using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Reflection.Metadata.Ecma335;

namespace HerculesApi.Models
{
    public class Geral
    {
        [Key]
        public int id { get; set; }
        [JsonProperty("ID")]
        public int? grl_id { get; set; }
        [JsonProperty("UC")]
        public int? grl_uc { get; set; }
        [NotMapped]
        public string? grl_tipoServ { get; set; }
        [NotMapped]
        public int? grl_na { get; set; }
        [JsonProperty("Nome")]
        public string? grl_nome { get; set; }
        [JsonProperty("Município")]
        public string? grl_municipio { get; set; }
        [JsonProperty("Núcleo")]
        public string? grl_nucleo { get; set; }
        [JsonProperty("Logradouro")]
        public string? grl_logradouro { get; set; }
        [JsonProperty("Número")]
        public string? grl_numero { get; set; }
        [JsonProperty("Complemento")]
        public string? grl_complemento { get; set; }
        [JsonProperty("Bloco")]
        public string? grl_bloco { get; set; }
        [JsonProperty("CP")]
        public int? grl_cp { get; set; }
        [JsonProperty("CS ID")]
        public int? grl_cs { get; set; }
        [JsonProperty("Posição 1")]
        public string? grl_pos1 { get; set; }
        [JsonProperty("Posição 2")]
        public string? grl_pos2 { get; set; }
        [JsonProperty("Posição 3")]
        public string? grl_pos3 { get; set; }
        [JsonProperty("ET")]
        public int? grl_et { get; set; }
        [JsonProperty("Módulo")]
        public int? grl_modulo { get; set; }
        [JsonProperty("Display")]
        public string? grl_display { get; set; }
        [JsonProperty("Motivo")]
        public string? grl_motivo { get; set; }
        [JsonProperty("Empreiteira")]
        public string? grl_empreiteira { get; set; }
        [JsonProperty("Status UC")]
        public string? grl_status_uc { get; set; }
        [JsonProperty("Situação")]
        public string? grl_situacao { get; set; }
        [JsonProperty("GPS")]
        public string? grl_gps { get; set; }
        public string? grl_obs { get; set; }
        public int? grl_vinculada { get; set; }
        [NotMapped]
        public string? motivo { get; set; }
        [NotMapped]
        public int? grl_correcao { get; set; }
        [NotMapped]
        public int? grl_provisorio { get; set; }
        [NotMapped]
        public int? grl_retirada { get; set; }
    }
}