﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using HerculesApi.Database;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using HerculesApi.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Microsoft.AspNetCore.Authorization;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.Extensions.Hosting.Internal;
using System.Globalization;
using System.IO;
using System.Threading;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using Microsoft.IdentityModel.Tokens;

namespace HerculesApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class ViewResumoController : ControllerBase
    {
        private readonly HerculesDbContext _context;
        private readonly AdmDbContext _contextAdm;
        private readonly Microsoft.AspNetCore.Hosting.IHostingEnvironment hostingEnvironment;
        public ViewResumoController(HerculesDbContext context, AdmDbContext contextAdm)
        {
            _context = context;
            _contextAdm = contextAdm;
        }
        
        [HttpGet("visitas/periodo/{dtinicial}/{dtfinal}/{tecId}")]
        public async Task<ActionResult<IEnumerable<ViewResumoDto>>> GetVisitasPeriodo(DateTime dtinicial, DateTime dtfinal, int tecId)
        {


            dtinicial = new DateTime(dtinicial.Year, dtinicial.Month, dtinicial.Day, 0, 1, 0);
            dtfinal = new DateTime(dtfinal.Year, dtfinal.Month, dtfinal.Day, 23, 59, 0);

            var resumo = await _context.vw_resumo
                        .Where(v => (v.data >= dtinicial && v.data <= dtfinal) && v.tecnicoId ==tecId)
                        .ToListAsync();
            ViewResumoDto resumoDto = new ViewResumoDto();
            resumoDto.resumo = resumo;
            resumoDto.totalNotas = resumo.Count;
            

            return Ok(resumoDto);
        }
    }
}