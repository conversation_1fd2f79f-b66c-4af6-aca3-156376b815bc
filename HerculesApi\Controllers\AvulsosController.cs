using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using HerculesApi.Database;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using HerculesApi.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Microsoft.AspNetCore.Authorization;

namespace HerculesApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class AvulsosController : ControllerBase
    {
        private readonly HerculesDbContext _context;
        public AvulsosController(HerculesDbContext context)
        {
            _context = context;
        }
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Avulso>>> GetAvulsos()
        {
            var avulsos = _context.Avulsos.ToList();
            if (avulsos.Count > 0)
            {
                return avulsos;
            }
            else
            {
                return NotFound("Não há clientes avulsos");
            }
        }
        [HttpGet("{id}")]
        public async Task<ActionResult<Avulso>> GetAvulso(int id)
        {
            var avulsos = await _context.Avulsos.FindAsync(id);
            if (avulsos == null)
            {
                return NotFound("Cliente não encontrado");
            }

            return avulsos;
        }
        [HttpPost]
        public async Task<ActionResult<Avulso>> PostAvulso(Avulso avulso)
        {
            if (avulso.avu_complemento != null)
            {
                var av = _context.Avulsos
                                .Where(x => x.avu_nome.Equals(avulso.avu_nome.ToLower())
                                && x.avu_logradouro.Equals(avulso.avu_logradouro.ToLower())
                                && x.avu_numero == avulso.avu_numero
                                && x.avu_complemento.Equals(avulso.avu_complemento.ToLower()))
                                .AsNoTracking()
                                .FirstOrDefault();
                if (av != null)
                {
                    return BadRequest(new { message = "Não é possível cadastrar cliente avulso já cadastrado!" });
                }
                else
                {
                    avulso.avu_nome = avulso.avu_nome.ToLower();
                    avulso.avu_logradouro = avulso.avu_logradouro.ToLower();
                    avulso.avu_complemento = avulso.avu_complemento.ToLower();
                    avulso.avu_motivoId = avulso.motivo.id;

                    _context.Avulsos.Add(avulso);
                    await _context.SaveChangesAsync();

                    var a = _context.vw_avulsos.Where(x=>x.id == avulso.id).FirstOrDefault();
                    OrdemServico or = new OrdemServico();
                    or.ord_na = 0;
                    or.ord_ordem = "";
                    or.ord_cliId = 0;
                    or.ord_uc = a.uc;
                    or.ord_nome = a.nome;
                    or.ord_municipio = a.municipio;
                    or.ord_nucleo = a.nucleo;
                    or.ord_logradouro = a.logradouro;
                    or.ord_numero = a.numero;
                    or.ord_complemento = a.complemento;
                    or.ord_bloco = "";
                    or.ord_cp = a.cp;
                    or.ord_cs = a.cs;
                    or.ord_pos1 = a.pos1;
                    or.ord_pos2 = a.pos2;
                    or.ord_pos3 = a.pos3;
                    or.ord_et = a.et;
                    or.ord_modulo = a.modulo;
                    or.ord_display = a.display;
                    or.ord_motivo = a.motivo;
                    or.ord_empreiteira = "";
                    or.ord_status_uc = "";
                    or.ord_situacao = "";
                    or.ord_gps = a.gps;
                    or.ord_dataEntrada = DateTime.Now;
                    or.ord_dataFechamento = null;
                    or.ord_status = 1;
                    or.ord_obs = "";
                    or.ord_tecnicos = a.tecnico;

                    return Ok(avulso);
                }
            }

            else
            {
                var av = _context.Avulsos
                                .Where(x => x.avu_nome.Equals(avulso.avu_nome.ToLower())
                                && x.avu_logradouro.Equals(avulso.avu_logradouro.ToLower())
                                && x.avu_numero == avulso.avu_numero)
                                .AsNoTracking()
                                .FirstOrDefault();
                if (av != null)
                {
                    return BadRequest(new { message = "Não é possível cadastrar cliente avulso já cadastrado!" });
                }
                else
                {
                    avulso.avu_nome = avulso.avu_nome.ToLower();
                    avulso.avu_logradouro = avulso.avu_logradouro.ToLower();
                    avulso.avu_motivoId = avulso.motivo.id;

                    _context.Avulsos.Add(avulso);
                    await _context.SaveChangesAsync();

                    var a = _context.vw_avulsos.Where(x => x.id == avulso.id).FirstOrDefault();
                    OrdemServico or = new OrdemServico();
                    or.ord_na = 0;
                    or.ord_ordem = "";
                    or.ord_cliId = 0;
                    or.ord_uc = a.uc;
                    or.ord_nome = a.nome;
                    or.ord_municipio = a.municipio;
                    or.ord_nucleo = a.nucleo;
                    or.ord_logradouro = a.logradouro;
                    or.ord_numero = a.numero;
                    or.ord_complemento = a.complemento;
                    or.ord_bloco = "";
                    or.ord_cp = a.cp;
                    or.ord_cs = a.cs;
                    or.ord_pos1 = a.pos1;
                    or.ord_pos2 = a.pos2;
                    or.ord_pos3 = a.pos3;
                    or.ord_et = a.et;
                    or.ord_modulo = a.modulo;
                    or.ord_display = a.display;
                    or.ord_motivo = a.motivo;
                    or.ord_empreiteira = "";
                    or.ord_status_uc = "";
                    or.ord_situacao = "";
                    or.ord_gps = a.gps;
                    or.ord_dataEntrada = DateTime.Now;
                    or.ord_dataFechamento = null;
                    or.ord_status = 1;
                    or.ord_obs = "";
                    or.ord_tecnicos = a.tecnico;

                    return Ok(avulso);
                }
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> PutAvulso(int id, Avulso avulso)
        {
            var avu = _context.Avulsos.Where(x => x.id == id).AsNoTracking().FirstOrDefault();
            if (avu == null)
            {
                return BadRequest("Cliente avulso não encontrado");
            }

            _context.Entry(avulso).State = EntityState.Modified;
            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!AvulsoExists(id))
                {
                    return NotFound("Cliente avulso não encontrado");
                }
                else
                {
                    throw;
                }
            }

            return Ok("Cliente avulso alterado com sucesso!");
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteAvulso(int id)
        {
            var avulso = await _context.Avulsos.FindAsync(id);
            if (avulso == null)
            {
                return NotFound("Cliente avulso não encontrado");
            }
            else
            {
                _context.Avulsos.Remove(avulso);
                await _context.SaveChangesAsync();

                return Ok("Cliente avulso excluido com sucesso!");
            }

        }
        private bool AvulsoExists(int id)
        {
            return _context.Avulsos.Any(e => e.id == id);
        }
    }
}