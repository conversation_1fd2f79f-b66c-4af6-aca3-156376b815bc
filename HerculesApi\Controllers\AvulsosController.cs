using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using HerculesApi.Database;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using HerculesApi.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Microsoft.AspNetCore.Authorization;

namespace HerculesApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class AvulsosController : ControllerBase
    {
        private readonly HerculesDbContext _context;
        public AvulsosController(HerculesDbContext context)
        {
            _context = context;
        }
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Avulso>>> GetAvulsos()
        {
            var avulsos = _context.Avulsos.ToList();
            if (avulsos.Count > 0)
            {
                return avulsos;
            }
            else
            {
                return NotFound("Não há clientes avulsos");
            }
        }
        [HttpGet("{id}")]
        public async Task<ActionResult<Avulso>> GetAvulso(int id)
        {
            var avulsos = await _context.Avulsos.FindAsync(id);
            if (avulsos == null)
            {
                return NotFound("Cliente não encontrado");
            }

            return avulsos;
        }
        [HttpPost]
        public async Task<ActionResult<Avulso>> PostAvulso(AvulsoCreateDto avulsoDto)
        {
            try
            {
                // Criar objeto Avulso a partir do DTO
                var avulso = new Avulso
                {
                    avu_nome = avulsoDto.avu_nome?.ToLower(),
                    avu_logradouro = avulsoDto.avu_logradouro?.ToLower(),
                    avu_numero = avulsoDto.avu_numero,
                    avu_complemento = avulsoDto.avu_complemento?.ToLower(),
                    avu_cp = avulsoDto.avu_cp,
                    avu_cs = avulsoDto.avu_cs,
                    avu_et = avulsoDto.avu_et,
                    avu_modulo = avulsoDto.avu_modulo,
                    avu_display = avulsoDto.avu_display,
                    avu_pos1 = avulsoDto.avu_pos1,
                    avu_pos2 = avulsoDto.avu_pos2,
                    avu_pos3 = avulsoDto.avu_pos3,
                    avu_motivoId = avulsoDto.avu_motivoId,
                    avu_municipioId = avulsoDto.avu_municipioId,
                    avu_nucleoId = avulsoDto.avu_nucleoId,
                    avu_tecnico = avulsoDto.avu_tecnico,
                    avu_tanque = avulsoDto.avu_tanque,
                    avu_uc = avulsoDto.avu_uc
                };

                // Verificar se já existe um avulso com os mesmos dados
                var existingAvulso = await _context.Avulsos
                    .Where(x => x.avu_nome == avulso.avu_nome
                            && x.avu_logradouro == avulso.avu_logradouro
                            && x.avu_numero == avulso.avu_numero
                            && (string.IsNullOrEmpty(avulso.avu_complemento) || x.avu_complemento == avulso.avu_complemento))
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                if (existingAvulso != null)
                {
                    return BadRequest(new { message = "Não é possível cadastrar cliente avulso já cadastrado!" });
                }

                // Salvar o avulso
                _context.Avulsos.Add(avulso);
                await _context.SaveChangesAsync();

                // Buscar dados completos do avulso criado
                var avulsoCompleto = await _context.vw_avulsos
                    .Where(x => x.id == avulso.id)
                    .FirstOrDefaultAsync();

                if (avulsoCompleto != null)
                {
                    // Criar ordem de serviço
                    var ordemServico = new OrdemServico
                    {
                        ord_na = 0,
                        ord_ordem = "",
                        ord_cliId = 0,
                        ord_uc = avulsoCompleto.uc,
                        ord_nome = avulsoCompleto.nome,
                        ord_municipio = avulsoCompleto.municipio,
                        ord_nucleo = avulsoCompleto.nucleo,
                        ord_logradouro = avulsoCompleto.logradouro,
                        ord_numero = avulsoCompleto.numero,
                        ord_complemento = avulsoCompleto.complemento,
                        ord_bloco = "",
                        ord_cp = avulsoCompleto.cp,
                        ord_cs = avulsoCompleto.cs,
                        ord_pos1 = avulsoCompleto.pos1,
                        ord_pos2 = avulsoCompleto.pos2,
                        ord_pos3 = avulsoCompleto.pos3,
                        ord_et = avulsoCompleto.et,
                        ord_modulo = avulsoCompleto.modulo,
                        ord_display = avulsoCompleto.display,
                        ord_motivo = avulsoCompleto.motivo,
                        ord_empreiteira = "",
                        ord_status_uc = "",
                        ord_situacao = "",
                        ord_gps = avulsoCompleto.gps,
                        ord_dataEntrada = DateTime.Now,
                        ord_dataFechamento = null,
                        ord_status = 1,
                        ord_obs = "",
                        ord_tecnicos = avulsoCompleto.tecnico
                    };

                    _context.OrdensServico.Add(ordemServico);
                    await _context.SaveChangesAsync();
                }

                return Ok(new
                {
                    message = "Cliente avulso cadastrado com sucesso!",
                    avulso = avulso
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    message = "Erro interno do servidor",
                    error = ex.Message
                });
            }
        }



        [HttpPut("{id}")]
        public async Task<IActionResult> PutAvulso(int id, Avulso avulso)
        {
            var avu = _context.Avulsos.Where(x => x.id == id).AsNoTracking().FirstOrDefault();
            if (avu == null)
            {
                return BadRequest("Cliente avulso não encontrado");
            }

            _context.Entry(avulso).State = EntityState.Modified;
            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!AvulsoExists(id))
                {
                    return NotFound("Cliente avulso não encontrado");
                }
                else
                {
                    throw;
                }
            }

            return Ok("Cliente avulso alterado com sucesso!");
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteAvulso(int id)
        {
            var avulso = await _context.Avulsos.FindAsync(id);
            if (avulso == null)
            {
                return NotFound("Cliente avulso não encontrado");
            }
            else
            {
                _context.Avulsos.Remove(avulso);
                await _context.SaveChangesAsync();

                return Ok("Cliente avulso excluido com sucesso!");
            }

        }
        private bool AvulsoExists(int id)
        {
            return _context.Avulsos.Any(e => e.id == id);
        }
    }
}