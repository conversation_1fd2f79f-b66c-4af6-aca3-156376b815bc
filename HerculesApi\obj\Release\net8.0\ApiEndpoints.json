[{"ContainingType": "HerculesApi.Controllers.AdequacoesController", "Method": "GetAdequacoes", "RelativePath": "api/Adequacoes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[HerculesApi.Models.Adequacao, HerculesApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.AdequacoesController", "Method": "PostAdequacoes", "RelativePath": "api/Adequacoes", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "adequacoes", "Type": "System.Collections.Generic.List`1[[HerculesApi.Models.Adequacao, HerculesApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "HerculesApi.Models.Adequacao", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.AdequacoesController", "Method": "Delete", "RelativePath": "api/Adequacoes", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.AgendamentosController", "Method": "GetAgendamentos", "RelativePath": "api/Agendamentos", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[HerculesApi.Models.Agendamento, HerculesApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.AgendamentosController", "Method": "PostAgendamento", "RelativePath": "api/Agendamentos", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "agendamento", "Type": "HerculesApi.Models.Agendamento", "IsRequired": true}], "ReturnTypes": [{"Type": "HerculesApi.Models.Agendamento", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.AgendamentosController", "Method": "GetAgendamento", "RelativePath": "api/Agendamentos/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "HerculesApi.Models.Agendamento", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.AgendamentosController", "Method": "PutAgendamento", "RelativePath": "api/Agendamentos/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "agendamento", "Type": "HerculesApi.Models.Agendamento", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.AgendamentosController", "Method": "DeleteAgendamento", "RelativePath": "api/Agendamentos/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.ArquivosController", "Method": "GetArquivoOuvidoria", "RelativePath": "api/Arquivos/ouvidoria/{na}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "na", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "HerculesApi.Models.Arquivo", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.ArquivosController", "Method": "UploadArquivo", "RelativePath": "api/Arquivos/upload/{tipo}/{id}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "tipo", "Type": "System.String", "IsRequired": false}, {"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "tipo", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.ArquivosController", "Method": "UploadArquivoOuvidoria", "RelativePath": "api/Arquivos/upload/ouvidoria", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "na", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.ArteriApiController", "Method": "ConsumirArteriApi", "RelativePath": "api/ArteriApi/consumir-arteri-api", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.EmpreiteirasController", "Method": "GetEmpreiteiras", "RelativePath": "api/Em<PERSON>ite<PERSON>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[HerculesApi.Models.Empreiteira, HerculesApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.EmpreiteirasController", "Method": "PostEmpreiteira", "RelativePath": "api/Em<PERSON>ite<PERSON>", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "HerculesApi.Models.Empreiteira", "IsRequired": true}], "ReturnTypes": [{"Type": "HerculesApi.Models.Empreiteira", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.EmpreiteirasController", "Method": "GetEmpreiteira", "RelativePath": "api/Empreiteira<PERSON>/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "HerculesApi.Models.Empreiteira", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.EmpreiteirasController", "Method": "PutEmpreiteira", "RelativePath": "api/Empreiteira<PERSON>/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "HerculesApi.Models.Empreiteira", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.EmpreiteirasController", "Method": "DeleteEmpreiteira", "RelativePath": "api/Empreiteira<PERSON>/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.EquipamentosController", "Method": "GetEquipamentos", "RelativePath": "api/Equipamentos", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[HerculesApi.Models.Equipamento, HerculesApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.EquipamentosController", "Method": "PostEquipamento", "RelativePath": "api/Equipamentos", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "equipamento", "Type": "HerculesApi.Models.Equipamento", "IsRequired": true}], "ReturnTypes": [{"Type": "HerculesApi.Models.Equipamento", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.EquipamentosController", "Method": "GetEquipamento", "RelativePath": "api/Equipamentos/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "HerculesApi.Models.Equipamento", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.EquipamentosController", "Method": "PutEquipamento", "RelativePath": "api/Equipamentos/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "equipamento", "Type": "HerculesApi.Models.Equipamento", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.EquipamentosController", "Method": "DeleteEquipamento", "RelativePath": "api/Equipamentos/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.GeraisController", "Method": "GetGeral", "RelativePath": "api/<PERSON>is", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[HerculesApi.Models.Geral, HerculesApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.GeraisController", "Method": "PostGeral", "RelativePath": "api/<PERSON>is", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "gerais", "Type": "System.Collections.Generic.List`1[[HerculesApi.Models.Geral, HerculesApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "HerculesApi.Models.Geral", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.GeraisController", "Method": "Delete", "RelativePath": "api/<PERSON>is", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.GeraisController", "Method": "UploadTxt", "RelativePath": "api/G<PERSON><PERSON>/upload-txt", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.LogErrosController", "Method": "GetLogErros", "RelativePath": "api/Log<PERSON>rros", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[HerculesApi.Models.LogErro, HerculesApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.LogErrosController", "Method": "PostLogErro", "RelativePath": "api/Log<PERSON>rros", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "logErro", "Type": "HerculesApi.Models.LogErro", "IsRequired": true}], "ReturnTypes": [{"Type": "HerculesApi.Models.LogErro", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.LogErrosController", "Method": "GetLogErro", "RelativePath": "api/LogErros/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "HerculesApi.Models.LogErro", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.LogErrosController", "Method": "PutLogErro", "RelativePath": "api/LogErros/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "logErro", "Type": "HerculesApi.Models.LogErro", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.LogErrosController", "Method": "DeleteLogErro", "RelativePath": "api/LogErros/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.LoginController", "Method": "PostAsync", "RelativePath": "api/Login/Login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userLogin", "Type": "HerculesApi.Models.UserLogin", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.MotivosController", "Method": "GetMotivos", "RelativePath": "api/Motivos", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[HerculesApi.Models.Motivo, HerculesApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.MotivosController", "Method": "PostEquipamento", "RelativePath": "api/Motivos", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "motivo", "Type": "HerculesApi.Models.Motivo", "IsRequired": true}], "ReturnTypes": [{"Type": "HerculesApi.Models.Motivo", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.MotivosController", "Method": "GetMotivo", "RelativePath": "api/Motivos/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "HerculesApi.Models.Motivo", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.MotivosController", "Method": "PutMotivo", "RelativePath": "api/Motivos/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "motivo", "Type": "HerculesApi.Models.Motivo", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.MotivosController", "Method": "DeleteMotivo", "RelativePath": "api/Motivos/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.MunicipiosController", "Method": "GetMunicipios", "RelativePath": "api/Mu<PERSON><PERSON><PERSON>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[HerculesApi.Models.Municipio, HerculesApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.MunicipiosController", "Method": "PostMunicipio", "RelativePath": "api/Mu<PERSON><PERSON><PERSON>", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "municipio", "Type": "HerculesApi.Models.Municipio", "IsRequired": true}], "ReturnTypes": [{"Type": "HerculesApi.Models.Municipio", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.MunicipiosController", "Method": "GetMunicipio", "RelativePath": "api/Municipios/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "HerculesApi.Models.Municipio", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.MunicipiosController", "Method": "PutMunicipio", "RelativePath": "api/Municipios/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "municipio", "Type": "HerculesApi.Models.Municipio", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.MunicipiosController", "Method": "DeleteMunicipio", "RelativePath": "api/Municipios/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.MunicipiosController", "Method": "GetMunicipiosGerais", "RelativePath": "api/<PERSON>nicip<PERSON>/gerais", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[HerculesApi.Models.ViewMunicipio, HerculesApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.MunicipiosController", "Method": "GetMunicipiosNucleo", "RelativePath": "api/Municipios/nucleo/{nucleoid}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "nucleoid", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.MunicipiosController", "Method": "GetMunicipiosServico", "RelativePath": "api/<PERSON><PERSON><PERSON><PERSON>/servico", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.NucleosController", "Method": "GetNucleos", "RelativePath": "api/Nucleos", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[HerculesApi.Models.Nucleo, HerculesApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.NucleosController", "Method": "PostNucleo", "RelativePath": "api/Nucleos", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "nucleo", "Type": "HerculesApi.Models.Nucleo", "IsRequired": true}], "ReturnTypes": [{"Type": "HerculesApi.Models.Nucleo", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.NucleosController", "Method": "GetNucleo", "RelativePath": "api/Nucleos/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "HerculesApi.Models.Nucleo", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.NucleosController", "Method": "PutNucleo", "RelativePath": "api/Nucleos/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "nucleo", "Type": "HerculesApi.Models.Nucleo", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.NucleosController", "Method": "DeleteNucleo", "RelativePath": "api/Nucleos/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.NucleosController", "Method": "GetNucleoGerais", "RelativePath": "api/Nucleos/gerais/municipio/{municipioid}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "municipioid", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[HerculesApi.Models.ViewNucleo, HerculesApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.NucleosController", "Method": "ImportarNucleos", "RelativePath": "api/Nucleos/importar-nucleos", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.NucleosController", "Method": "GetNucleoMunicipios", "RelativePath": "api/Nucleos/municipio/{municipioid}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "municipioid", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[HerculesApi.Models.Nucleo, HerculesApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.OrdensServicoController", "Method": "PostOrdem", "RelativePath": "api/OrdensServic<PERSON>", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "sele<PERSON>s", "Type": "HerculesApi.Models.OrdemDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.OrdensServicoController", "Method": "PutOrdem", "RelativePath": "api/OrdensServico/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "usu", "Type": "HerculesApi.Models.UsuarioDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.OrdensServicoController", "Method": "DeleteOrdem", "RelativePath": "api/OrdensServico/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.OrdensServicoController", "Method": "GetOrdens", "RelativePath": "api/OrdensServico/{pageNumber}/{pageSize}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageNumber", "Type": "System.Int32", "IsRequired": true}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[HerculesApi.Models.ViewOrdemServico, HerculesApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.OrdensServicoController", "Method": "GetOrdensNucleo", "RelativePath": "api/OrdensServico/nucleo/ordemid/{ordemid}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ordemid", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[HerculesApi.Models.OrdemServico, HerculesApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.OrdensServicoController", "Method": "PutOrdemStatus", "RelativePath": "api/OrdensServico/status/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "status", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.OrdensServicoController", "Method": "GetOrdemUsuario", "RelativePath": "api/OrdensServico/userid/{userid}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userid", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.OuvidoriasController", "Method": "GetOuvidorias", "RelativePath": "api/Ouvid<PERSON>s", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[HerculesApi.Models.Ouvidoria, HerculesApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.OuvidoriasController", "Method": "GetOuvidoria", "RelativePath": "api/Ouvidorias/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "HerculesApi.Models.Ouvidoria", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.OuvidoriasController", "Method": "PutOuvidoria", "RelativePath": "api/Ouvidorias/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "ouvid<PERSON>", "Type": "HerculesApi.Models.Ouvidoria", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.OuvidoriasController", "Method": "DeleteOuvidoria", "RelativePath": "api/Ouvidorias/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.OuvidoriasController", "Method": "PostOuvidoria", "RelativePath": "api/Ouvidorias/{na}/{obs}/{municipioid}/{nucleoid}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "na", "Type": "System.Int32", "IsRequired": true}, {"Name": "obs", "Type": "System.String", "IsRequired": true}, {"Name": "municipioid", "Type": "System.Int32", "IsRequired": true}, {"Name": "nucleoid", "Type": "System.Int32", "IsRequired": true}, {"Name": "cli", "Type": "HerculesApi.Models.ClienteDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.OuvidoriasController", "Method": "GetOuvidoriaUsuarioMob", "RelativePath": "api/Ouvidorias/mobile/userid/{userid}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userid", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[HerculesApi.Models.OuvidoriaMob, HerculesApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.OuvidoriasController", "Method": "PutOuvidoriaStatus", "RelativePath": "api/Ouvidorias/status/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "status", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.OuvidoriasController", "Method": "GetOuvidoriaUsuario", "RelativePath": "api/Ouvidorias/userid/{userid}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userid", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[HerculesApi.Models.Ouvidoria, HerculesApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.TiposDeCorrecaoController", "Method": "GetTiposCorrecao", "RelativePath": "api/TiposDeCorrecao", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[HerculesApi.Models.TipoCorrecao, HerculesApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.TiposDeCorrecaoController", "Method": "PostTipoCorrecao", "RelativePath": "api/TiposDeCorrecao", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "tpCorrecao", "Type": "HerculesApi.Models.TipoCorrecao", "IsRequired": true}], "ReturnTypes": [{"Type": "HerculesApi.Models.TipoCorrecao", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.TiposDeCorrecaoController", "Method": "GetTipoCorrecao", "RelativePath": "api/TiposDeCorrecao/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "HerculesApi.Models.TipoCorrecao", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.TiposDeCorrecaoController", "Method": "PutMotivo", "RelativePath": "api/TiposDeCorrecao/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "tpCorrecao", "Type": "HerculesApi.Models.TipoCorrecao", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.TiposDeCorrecaoController", "Method": "DeleteTipoCorrecao", "RelativePath": "api/TiposDeCorrecao/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.TiposDeServico", "Method": "GetTiposServic<PERSON>", "RelativePath": "api/TiposDeServico", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[HerculesApi.Models.TipoServico, HerculesApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.TiposDeServico", "Method": "PostTipo<PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/TiposDeServico", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "tipoServico", "Type": "HerculesApi.Models.TipoServico", "IsRequired": true}], "ReturnTypes": [{"Type": "HerculesApi.Models.TipoServico", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.TiposDeServico", "Method": "GetTipo<PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/TiposDeServico/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "HerculesApi.Models.TipoServico", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.TiposDeServico", "Method": "Put<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/TiposDeServico/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "tipoServico", "Type": "HerculesApi.Models.TipoServico", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.TiposDeServico", "Method": "DeleteTipo<PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/TiposDeServico/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.ViewVisitasController", "Method": "GetEmpreiteiras", "RelativePath": "api/ViewVisitas", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[HerculesApi.Models.ViewVisita, HerculesApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.VisitasController", "Method": "GetVisitas", "RelativePath": "api/Visitas", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[HerculesApi.Models.Visita, HerculesApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.VisitasController", "Method": "PostVisita", "RelativePath": "api/Visitas", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "visita", "Type": "HerculesApi.Models.Visita", "IsRequired": true}], "ReturnTypes": [{"Type": "HerculesApi.Models.Visita", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.VisitasController", "Method": "GetVisita", "RelativePath": "api/Visitas/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "HerculesApi.Models.Visita", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HerculesApi.Controllers.VisitasController", "Method": "PutVisita", "RelativePath": "api/Visitas/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "visita", "Type": "HerculesApi.Models.Visita", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HerculesApi.Controllers.VisitasController", "Method": "DeleteVisita", "RelativePath": "api/Visitas/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}]