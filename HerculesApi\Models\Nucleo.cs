﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HerculesApi.Models
{
    public class Nucleo
    {
        [Key]
        public int id { get; set; }
        public string nuc_descricao { get; set; }
        public string nuc_gps { get; set; }
        [NotMapped]
        public int? munId { get; set; }
        [NotMapped]
        public int? empreiteiraId { get; set; }
        public Nucleo()
        {
            munId = new int();
        }
    }
}
