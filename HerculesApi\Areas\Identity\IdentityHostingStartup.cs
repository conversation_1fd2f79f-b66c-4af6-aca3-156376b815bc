﻿using System;
using baramaiamv.Areas.Identity.Data;
using baramaiamv.Data;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.UI;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

[assembly: HostingStartup(typeof(baramaiamv.Areas.Identity.IdentityHostingStartup))]
namespace baramaiamv.Areas.Identity
{
    public class IdentityHostingStartup : IHostingStartup
    {
        public void Configure(IWebHostBuilder builder)
        {
            builder.ConfigureServices((context, services) => {
                services.AddDbContext<baramaiamvContext>(options =>
                    options.UseSqlServer(
                        context.Configuration.GetConnectionString("baramaiamvContextConnection")));

                services.AddDefaultIdentity<baramaiamvUser>(options => options.SignIn.RequireConfirmedAccount = true)
                    .AddEntityFrameworkStores<baramaiamvContext>();
            });
        }
    }
}