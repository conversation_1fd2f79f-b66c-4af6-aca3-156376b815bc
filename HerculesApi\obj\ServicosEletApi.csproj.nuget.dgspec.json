{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\GitHub\\ServicosEletApi\\ServicosEletApi\\ServicosEletApi.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\GitHub\\ServicosEletApi\\ServicosEletApi\\ServicosEletApi.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\GitHub\\ServicosEletApi\\ServicosEletApi\\ServicosEletApi.csproj", "projectName": "ServicosElet<PERSON>pi", "projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\ServicosEletApi\\ServicosEletApi\\ServicosEletApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\GitHub\\ServicosEletApi\\ServicosEletApi\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNet.Identity.Core": {"target": "Package", "version": "[2.2.4, )"}, "Microsoft.AspNet.Identity.Owin": {"target": "Package", "version": "[2.2.4, )"}, "Microsoft.AspNet.WebApi.Core": {"target": "Package", "version": "[5.3.0, )"}, "Microsoft.AspNetCore.Authentication": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.10, )"}, "Microsoft.AspNetCore.Identity": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[8.0.10, )"}, "Microsoft.AspNetCore.Identity.UI": {"target": "Package", "version": "[8.0.10, )"}, "Microsoft.AspNetCore.JsonPatch": {"target": "Package", "version": "[8.0.10, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.10, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.10, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.10, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[8.0.10, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.10, )"}, "Microsoft.VisualStudio.Web.CodeGeneration.Design": {"target": "Package", "version": "[8.0.6, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[8.0.2, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.9.0, )"}, "System.Net.Http": {"target": "Package", "version": "[4.3.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}}}}