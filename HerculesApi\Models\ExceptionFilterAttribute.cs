﻿using System.Web.Http.Filters;

namespace HerculesApi.Models
{
    public class NotImplExceptionFilterAttribute : ExceptionFilterAttribute
    {
        public override void OnException(HttpActionExecutedContext context)
        {
            if(context.Exception != null)
            {
                var exception = context.Exception;
                var message = context.Exception.Message;
                var innermessage = context.Exception.InnerException;
                var path = context.Request.RequestUri.AbsolutePath;

            }
        }
    }
}
