{"runtimeTarget": {"name": ".NETCoreApp,Version=v5.0", "signature": ""}, "compilationOptions": {"defines": ["TRACE", "RELEASE", "NET", "NET5_0", "NETCOREAPP", "NET5_0_OR_GREATER", "NETCOREAPP1_0_OR_GREATER", "NETCOREAPP1_1_OR_GREATER", "NETCOREAPP2_0_OR_GREATER", "NETCOREAPP2_1_OR_GREATER", "NETCOREAPP2_2_OR_GREATER", "NETCOREAPP3_0_OR_GREATER", "NETCOREAPP3_1_OR_GREATER"], "languageVersion": "9.0", "platform": "", "allowUnsafe": false, "warningsAsErrors": false, "optimize": true, "keyFile": "", "emitEntryPoint": true, "xmlDoc": false, "debugType": "portable"}, "targets": {".NETCoreApp,Version=v5.0": {"mv/1.0.0": {"dependencies": {"Microsoft.AspNet.Identity.Core": "2.2.3", "Microsoft.AspNet.Identity.Owin": "2.2.3", "Microsoft.AspNet.WebApi.Core": "5.2.9", "Microsoft.AspNetCore.Authentication": "2.2.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "5.0.17", "Microsoft.AspNetCore.Identity": "2.2.0", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "5.0.17", "Microsoft.AspNetCore.Identity.UI": "5.0.17", "Microsoft.AspNetCore.JsonPatch": "5.0.16", "Microsoft.EntityFrameworkCore": "5.0.17", "Microsoft.EntityFrameworkCore.Design": "5.0.17", "Microsoft.EntityFrameworkCore.SqlServer": "5.0.17", "Microsoft.EntityFrameworkCore.Sqlite": "5.0.17", "Microsoft.EntityFrameworkCore.Tools": "5.0.17", "Microsoft.VisualStudio.Web.CodeGeneration.Design": "5.0.2", "Pomelo.EntityFrameworkCore.MySql": "5.0.4", "Swashbuckle.AspNetCore": "5.6.3", "Microsoft.AspNetCore.Antiforgery": "*******", "Microsoft.AspNetCore.Authentication.Abstractions.Reference": "*******", "Microsoft.AspNetCore.Authentication.Cookies.Reference": "*******", "Microsoft.AspNetCore.Authentication.Core.Reference": "*******", "Microsoft.AspNetCore.Authentication.Reference": "*******", "Microsoft.AspNetCore.Authentication.OAuth": "*******", "Microsoft.AspNetCore.Authorization": "*******", "Microsoft.AspNetCore.Authorization.Policy": "*******", "Microsoft.AspNetCore.Components.Authorization": "*******", "Microsoft.AspNetCore.Components": "*******", "Microsoft.AspNetCore.Components.Forms": "*******", "Microsoft.AspNetCore.Components.Server": "*******", "Microsoft.AspNetCore.Components.Web": "*******", "Microsoft.AspNetCore.Connections.Abstractions": "*******", "Microsoft.AspNetCore.CookiePolicy": "*******", "Microsoft.AspNetCore.Cors": "*******", "Microsoft.AspNetCore.DataProtection.Abstractions.Reference": "*******", "Microsoft.AspNetCore.DataProtection.Reference": "*******", "Microsoft.AspNetCore.DataProtection.Extensions": "*******", "Microsoft.AspNetCore.Diagnostics.Abstractions": "*******", "Microsoft.AspNetCore.Diagnostics": "*******", "Microsoft.AspNetCore.Diagnostics.HealthChecks": "*******", "Microsoft.AspNetCore": "*******", "Microsoft.AspNetCore.HostFiltering": "*******", "Microsoft.AspNetCore.Hosting.Abstractions.Reference": "*******", "Microsoft.AspNetCore.Hosting": "*******", "Microsoft.AspNetCore.Hosting.Server.Abstractions.Reference": "*******", "Microsoft.AspNetCore.Html.Abstractions.Reference": "*******", "Microsoft.AspNetCore.Http.Abstractions.Reference": "*******", "Microsoft.AspNetCore.Http.Connections.Common": "*******", "Microsoft.AspNetCore.Http.Connections": "*******", "Microsoft.AspNetCore.Http.Reference": "*******", "Microsoft.AspNetCore.Http.Extensions.Reference": "*******", "Microsoft.AspNetCore.Http.Features.Reference": "*******", "Microsoft.AspNetCore.HttpOverrides": "*******", "Microsoft.AspNetCore.HttpsPolicy": "*******", "Microsoft.AspNetCore.Identity.Reference": "*******", "Microsoft.AspNetCore.Localization": "*******", "Microsoft.AspNetCore.Localization.Routing": "*******", "Microsoft.AspNetCore.Metadata": "*******", "Microsoft.AspNetCore.Mvc.Abstractions": "*******", "Microsoft.AspNetCore.Mvc.ApiExplorer": "*******", "Microsoft.AspNetCore.Mvc.Core": "*******", "Microsoft.AspNetCore.Mvc.Cors": "*******", "Microsoft.AspNetCore.Mvc.DataAnnotations": "*******", "Microsoft.AspNetCore.Mvc": "*******", "Microsoft.AspNetCore.Mvc.Formatters.Json": "*******", "Microsoft.AspNetCore.Mvc.Formatters.Xml": "*******", "Microsoft.AspNetCore.Mvc.Localization": "*******", "Microsoft.AspNetCore.Mvc.Razor": "*******", "Microsoft.AspNetCore.Mvc.RazorPages": "*******", "Microsoft.AspNetCore.Mvc.TagHelpers": "*******", "Microsoft.AspNetCore.Mvc.ViewFeatures": "*******", "Microsoft.AspNetCore.Razor.Reference": "*******", "Microsoft.AspNetCore.Razor.Runtime.Reference": "*******", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "*******", "Microsoft.AspNetCore.ResponseCaching": "*******", "Microsoft.AspNetCore.ResponseCompression": "*******", "Microsoft.AspNetCore.Rewrite": "*******", "Microsoft.AspNetCore.Routing.Abstractions": "*******", "Microsoft.AspNetCore.Routing": "*******", "Microsoft.AspNetCore.Server.HttpSys": "*******", "Microsoft.AspNetCore.Server.IIS": "*******", "Microsoft.AspNetCore.Server.IISIntegration": "*******", "Microsoft.AspNetCore.Server.Kestrel.Core": "*******", "Microsoft.AspNetCore.Server.Kestrel": "*******", "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets": "*******", "Microsoft.AspNetCore.Session": "*******", "Microsoft.AspNetCore.SignalR.Common": "*******", "Microsoft.AspNetCore.SignalR.Core": "*******", "Microsoft.AspNetCore.SignalR": "*******", "Microsoft.AspNetCore.SignalR.Protocols.Json": "*******", "Microsoft.AspNetCore.StaticFiles": "*******", "Microsoft.AspNetCore.WebSockets": "*******", "Microsoft.AspNetCore.WebUtilities.Reference": "*******", "Microsoft.CSharp.Reference": "*******", "Microsoft.Extensions.Caching.Abstractions.Reference": "*******", "Microsoft.Extensions.Caching.Memory.Reference": "*******", "Microsoft.Extensions.Configuration.Abstractions.Reference": "*******", "Microsoft.Extensions.Configuration.Binder": "*******", "Microsoft.Extensions.Configuration.CommandLine": "*******", "Microsoft.Extensions.Configuration": "*******", "Microsoft.Extensions.Configuration.EnvironmentVariables": "*******", "Microsoft.Extensions.Configuration.FileExtensions": "*******", "Microsoft.Extensions.Configuration.Ini": "*******", "Microsoft.Extensions.Configuration.Json": "*******", "Microsoft.Extensions.Configuration.KeyPerFile": "*******", "Microsoft.Extensions.Configuration.UserSecrets": "*******", "Microsoft.Extensions.Configuration.Xml": "*******", "Microsoft.Extensions.DependencyInjection.Abstractions.Reference": "*******", "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "*******", "Microsoft.Extensions.Diagnostics.HealthChecks": "*******", "Microsoft.Extensions.FileProviders.Abstractions.Reference": "*******", "Microsoft.Extensions.FileProviders.Composite": "*******", "Microsoft.Extensions.FileProviders.Physical": "*******", "Microsoft.Extensions.FileSystemGlobbing": "*******", "Microsoft.Extensions.Hosting.Abstractions.Reference": "*******", "Microsoft.Extensions.Hosting": "*******", "Microsoft.Extensions.Http": "*******", "Microsoft.Extensions.Localization.Abstractions": "*******", "Microsoft.Extensions.Localization": "*******", "Microsoft.Extensions.Logging.Abstractions.Reference": "*******", "Microsoft.Extensions.Logging.Configuration": "*******", "Microsoft.Extensions.Logging.Console": "*******", "Microsoft.Extensions.Logging.Debug": "*******", "Microsoft.Extensions.Logging.Reference": "*******", "Microsoft.Extensions.Logging.EventLog": "*******", "Microsoft.Extensions.Logging.EventSource": "*******", "Microsoft.Extensions.Logging.TraceSource": "*******", "Microsoft.Extensions.ObjectPool.Reference": "*******", "Microsoft.Extensions.Options.ConfigurationExtensions": "*******", "Microsoft.Extensions.Options.DataAnnotations": "*******", "Microsoft.Extensions.Options.Reference": "*******", "Microsoft.Extensions.Primitives.Reference": "*******", "Microsoft.Extensions.WebEncoders.Reference": "*******", "Microsoft.JSInterop": "*******", "Microsoft.Net.Http.Headers.Reference": "*******", "Microsoft.VisualBasic.Core": "10.0.6.0", "Microsoft.VisualBasic": "10.0.0.0", "Microsoft.Win32.Primitives.Reference": "*******", "Microsoft.Win32.Registry.Reference": "*******", "mscorlib": "*******", "netstandard": "2.1.0.0", "System.AppContext.Reference": "*******", "System.Buffers.Reference": "*******", "System.Collections.Concurrent.Reference": "*******", "System.Collections.Reference": "*******", "System.Collections.Immutable.Reference": "*******", "System.Collections.NonGeneric.Reference": "*******", "System.Collections.Specialized.Reference": "*******", "System.ComponentModel.Annotations.Reference": "*******", "System.ComponentModel.DataAnnotations": "*******", "System.ComponentModel.Reference": "*******", "System.ComponentModel.EventBasedAsync": "*******", "System.ComponentModel.Primitives.Reference": "*******", "System.ComponentModel.TypeConverter.Reference": "*******", "System.Configuration": "*******", "System.Console.Reference": "*******", "System.Core": "*******", "System.Data.Common": "*******", "System.Data.DataSetExtensions": "*******", "System.Data": "*******", "System.Diagnostics.Contracts": "*******", "System.Diagnostics.Debug.Reference": "*******", "System.Diagnostics.EventLog": "*******", "System.Diagnostics.FileVersionInfo": "*******", "System.Diagnostics.Process": "*******", "System.Diagnostics.StackTrace": "*******", "System.Diagnostics.TextWriterTraceListener": "*******", "System.Diagnostics.Tools.Reference": "*******", "System.Diagnostics.TraceSource": "*******", "System.Diagnostics.Tracing.Reference": "*******", "System": "*******", "System.Drawing": "*******", "System.Drawing.Primitives": "*******", "System.Dynamic.Runtime": "*******", "System.Formats.Asn1": "*******", "System.Globalization.Calendars.Reference": "*******", "System.Globalization.Reference": "*******", "System.Globalization.Extensions.Reference": "*******", "System.IO.Compression.Brotli": "*******", "System.IO.Compression.Reference": "*******", "System.IO.Compression.FileSystem": "*******", "System.IO.Compression.ZipFile.Reference": "*******", "System.IO.Reference": "*******", "System.IO.FileSystem.Reference": "*******", "System.IO.FileSystem.DriveInfo": "*******", "System.IO.FileSystem.Primitives.Reference": "*******", "System.IO.FileSystem.Watcher": "*******", "System.IO.IsolatedStorage": "*******", "System.IO.MemoryMappedFiles": "*******", "System.IO.Pipelines": "*******", "System.IO.Pipes": "*******", "System.IO.UnmanagedMemoryStream": "*******", "System.Linq.Reference": "*******", "System.Linq.Expressions.Reference": "*******", "System.Linq.Parallel": "*******", "System.Linq.Queryable": "*******", "System.Memory.Reference": "*******", "System.Net": "*******", "System.Net.Http.Reference": "*******", "System.Net.Http.Json": "*******", "System.Net.HttpListener": "*******", "System.Net.Mail": "*******", "System.Net.NameResolution.Reference": "*******", "System.Net.NetworkInformation": "*******", "System.Net.Ping": "*******", "System.Net.Primitives.Reference": "*******", "System.Net.Requests": "*******", "System.Net.Security": "*******", "System.Net.ServicePoint": "*******", "System.Net.Sockets.Reference": "*******", "System.Net.WebClient": "*******", "System.Net.WebHeaderCollection": "*******", "System.Net.WebProxy": "*******", "System.Net.WebSockets.Client": "*******", "System.Net.WebSockets": "*******", "System.Numerics": "*******", "System.Numerics.Vectors": "*******", "System.ObjectModel.Reference": "*******", "System.Reflection.DispatchProxy": "*******", "System.Reflection.Reference": "*******", "System.Reflection.Emit.Reference": "*******", "System.Reflection.Emit.ILGeneration.Reference": "*******", "System.Reflection.Emit.Lightweight.Reference": "*******", "System.Reflection.Extensions.Reference": "*******", "System.Reflection.Metadata.Reference": "*******", "System.Reflection.Primitives.Reference": "*******", "System.Reflection.TypeExtensions.Reference": "*******", "System.Resources.Reader": "*******", "System.Resources.ResourceManager.Reference": "*******", "System.Resources.Writer": "*******", "System.Runtime.CompilerServices.Unsafe.Reference": "*******", "System.Runtime.CompilerServices.VisualC": "*******", "System.Runtime.Reference": "*******", "System.Runtime.Extensions.Reference": "*******", "System.Runtime.Handles.Reference": "*******", "System.Runtime.InteropServices.Reference": "*******", "System.Runtime.InteropServices.RuntimeInformation.Reference": "*******", "System.Runtime.Intrinsics": "*******", "System.Runtime.Loader": "*******", "System.Runtime.Numerics.Reference": "*******", "System.Runtime.Serialization": "*******", "System.Runtime.Serialization.Formatters.Reference": "*******", "System.Runtime.Serialization.Json.Reference": "*******", "System.Runtime.Serialization.Primitives.Reference": "*******", "System.Runtime.Serialization.Xml": "*******", "System.Security.AccessControl.Reference": "*******", "System.Security.Claims": "*******", "System.Security.Cryptography.Algorithms.Reference": "*******", "System.Security.Cryptography.Cng.Reference": "*******", "System.Security.Cryptography.Csp.Reference": "*******", "System.Security.Cryptography.Encoding.Reference": "*******", "System.Security.Cryptography.Primitives.Reference": "*******", "System.Security.Cryptography.X509Certificates.Reference": "*******", "System.Security.Cryptography.Xml.Reference": "*******", "System.Security": "*******", "System.Security.Permissions.Reference": "*******", "System.Security.Principal": "*******", "System.Security.Principal.Windows.Reference": "*******", "System.Security.SecureString.Reference": "*******", "System.ServiceModel.Web": "*******", "System.ServiceProcess": "*******", "System.Text.Encoding.CodePages.Reference": "*******", "System.Text.Encoding.Reference": "*******", "System.Text.Encoding.Extensions.Reference": "*******", "System.Text.Encodings.Web.Reference": "*******", "System.Text.Json": "*******", "System.Text.RegularExpressions.Reference": "*******", "System.Threading.Channels": "*******", "System.Threading.Reference": "*******", "System.Threading.Overlapped": "*******", "System.Threading.Tasks.Dataflow": "*******", "System.Threading.Tasks.Reference": "*******", "System.Threading.Tasks.Extensions.Reference": "*******", "System.Threading.Tasks.Parallel": "*******", "System.Threading.Thread": "*******", "System.Threading.ThreadPool": "*******", "System.Threading.Timer.Reference": "*******", "System.Transactions": "*******", "System.Transactions.Local": "*******", "System.ValueTuple": "*******", "System.Web": "*******", "System.Web.HttpUtility": "*******", "System.Windows": "*******", "System.Windows.Extensions.Reference": "*******", "System.Xml": "*******", "System.Xml.Linq": "*******", "System.Xml.ReaderWriter.Reference": "*******", "System.Xml.Serialization": "*******", "System.Xml.XDocument.Reference": "*******", "System.Xml.XmlDocument.Reference": "*******", "System.Xml.XmlSerializer.Reference": "*******", "System.Xml.XPath": "*******", "System.Xml.XPath.XDocument": "*******", "WindowsBase": "*******"}, "runtime": {"mv.dll": {}}, "compile": {"mv.dll": {}}}, "Humanizer.Core/2.8.26": {"runtime": {"lib/netstandard2.0/Humanizer.dll": {"assemblyVersion": "2.8.0.0", "fileVersion": "2.8.26.1919"}}}, "Microsoft.AspNet.Identity.Core/2.2.3": {"runtime": {"lib/net45/Microsoft.AspNet.Identity.Core.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.2.3.11125"}}, "compile": {"lib/net45/Microsoft.AspNet.Identity.Core.dll": {}}}, "Microsoft.AspNet.Identity.Owin/2.2.3": {"dependencies": {"Microsoft.AspNet.Identity.Core": "2.2.3", "Microsoft.Owin.Security": "3.0.1", "Microsoft.Owin.Security.Cookies": "3.0.1", "Microsoft.Owin.Security.OAuth": "3.0.1"}, "runtime": {"lib/net45/Microsoft.AspNet.Identity.Owin.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.2.3.11125"}}, "compile": {"lib/net45/Microsoft.AspNet.Identity.Owin.dll": {}}}, "Microsoft.AspNet.WebApi.Client/5.2.9": {"dependencies": {"Newtonsoft.Json": "12.0.2", "Newtonsoft.Json.Bson": "1.0.1"}, "runtime": {"lib/netstandard2.0/System.Net.Http.Formatting.dll": {"assemblyVersion": "5.2.9.0", "fileVersion": "5.2.61129.10"}}, "compile": {"lib/netstandard2.0/System.Net.Http.Formatting.dll": {}}}, "Microsoft.AspNet.WebApi.Core/5.2.9": {"dependencies": {"Microsoft.AspNet.WebApi.Client": "5.2.9"}, "runtime": {"lib/net45/System.Web.Http.dll": {"assemblyVersion": "5.2.9.0", "fileVersion": "5.2.61129.10"}}, "compile": {"lib/net45/System.Web.Http.dll": {}}}, "Microsoft.AspNetCore.Authentication/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.DataProtection": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Extensions.WebEncoders": "2.2.0"}}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}}, "Microsoft.AspNetCore.Authentication.Cookies/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication": "2.2.0"}}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0"}}, "Microsoft.AspNetCore.Authentication.JwtBearer/5.0.17": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.7.1"}, "runtime": {"lib/net5.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21507"}}, "compile": {"lib/net5.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {}}}, "Microsoft.AspNetCore.Cryptography.Internal/5.0.17": {"runtime": {"lib/net5.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21507"}}, "compile": {"lib/net5.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {}}}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/5.0.17": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "5.0.17"}, "runtime": {"lib/net5.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21507"}}, "compile": {"lib/net5.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {}}}, "Microsoft.AspNetCore.DataProtection/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "5.0.17", "Microsoft.AspNetCore.DataProtection.Abstractions": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Win32.Registry": "4.7.0", "System.Security.Cryptography.Xml": "4.5.0", "System.Security.Principal.Windows": "4.7.0"}}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.2.0": {}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "2.2.0"}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}}, "Microsoft.AspNetCore.Html.Abstractions/2.2.0": {"dependencies": {"System.Text.Encodings.Web": "4.5.0"}}, "Microsoft.AspNetCore.Http/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Net.Http.Headers": "2.2.0"}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "4.5.0"}}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Net.Http.Headers": "2.2.0", "System.Buffers": "4.5.0"}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.AspNetCore.Identity/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Cookies": "2.2.0", "Microsoft.AspNetCore.Cryptography.KeyDerivation": "5.0.17", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.Extensions.Identity.Core": "5.0.17"}}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/5.0.17": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "5.0.17", "Microsoft.Extensions.Identity.Stores": "5.0.17"}, "runtime": {"lib/net5.0/Microsoft.AspNetCore.Identity.EntityFrameworkCore.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21507"}}, "compile": {"lib/net5.0/Microsoft.AspNetCore.Identity.EntityFrameworkCore.dll": {}}}, "Microsoft.AspNetCore.Identity.UI/5.0.17": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "5.0.17", "Microsoft.Extensions.Identity.Stores": "5.0.17"}, "runtime": {"lib/net5.0/Microsoft.AspNetCore.Identity.UI.Views.V4.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21507"}, "lib/net5.0/Microsoft.AspNetCore.Identity.UI.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21507"}}, "compile": {"lib/net5.0/Microsoft.AspNetCore.Identity.UI.Views.V4.dll": {}, "lib/net5.0/Microsoft.AspNetCore.Identity.UI.dll": {}}}, "Microsoft.AspNetCore.JsonPatch/5.0.16": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "12.0.2"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.JsonPatch.dll": {"assemblyVersion": "5.0.16.0", "fileVersion": "5.0.1622.16706"}}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.JsonPatch.dll": {}}}, "Microsoft.AspNetCore.Razor/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Html.Abstractions": "2.2.0"}}, "Microsoft.AspNetCore.Razor.Language/5.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.52605"}}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {}}}, "Microsoft.AspNetCore.Razor.Runtime/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Html.Abstractions": "2.2.0", "Microsoft.AspNetCore.Razor": "2.2.0"}}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"dependencies": {"Microsoft.Net.Http.Headers": "2.2.0", "System.Text.Encodings.Web": "4.5.0"}}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.21406"}}, "compile": {"ref/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {}}}, "Microsoft.CodeAnalysis.Analyzers/3.0.0": {}, "Microsoft.CodeAnalysis.Common/3.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.0.0", "System.Collections.Immutable": "5.0.0", "System.Memory": "4.5.4", "System.Reflection.Metadata": "5.0.0", "System.Runtime.CompilerServices.Unsafe": "4.7.1", "System.Text.Encoding.CodePages": "4.7.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "3.8.0.0", "fileVersion": "3.800.20.56202"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}, "compile": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {}}}, "Microsoft.CodeAnalysis.CSharp/3.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "3.8.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "3.8.0.0", "fileVersion": "3.800.20.56202"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}, "compile": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/3.8.0": {"dependencies": {"Humanizer.Core": "2.8.26", "Microsoft.CodeAnalysis.CSharp": "3.8.0", "Microsoft.CodeAnalysis.Common": "3.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "3.8.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"assemblyVersion": "3.8.0.0", "fileVersion": "3.800.20.56202"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}, "compile": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {}}}, "Microsoft.CodeAnalysis.Razor/5.0.0": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "5.0.0", "Microsoft.CodeAnalysis.CSharp": "3.8.0", "Microsoft.CodeAnalysis.Common": "3.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.52605"}}, "compile": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll": {}}}, "Microsoft.CodeAnalysis.Workspaces.Common/3.8.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "Microsoft.CodeAnalysis.Common": "3.8.0", "System.Composition": "1.0.31"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.Workspaces.dll": {"assemblyVersion": "3.8.0.0", "fileVersion": "3.800.20.56202"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}, "compile": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.Workspaces.dll": {}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Data.SqlClient/2.0.1": {"dependencies": {"Microsoft.Data.SqlClient.SNI.runtime": "2.0.1", "Microsoft.Identity.Client": "4.14.0", "Microsoft.IdentityModel.JsonWebTokens": "6.7.1", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.7.1", "Microsoft.Win32.Registry": "4.7.0", "System.Configuration.ConfigurationManager": "4.7.0", "System.Diagnostics.DiagnosticSource": "5.0.1", "System.Runtime.Caching": "4.7.0", "System.Security.Principal.Windows": "4.7.0", "System.Text.Encoding.CodePages": "4.7.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "2.0.20168.4", "fileVersion": "2.0.20168.4"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "2.0.20168.4", "fileVersion": "2.0.20168.4"}, "runtimes/win/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "2.0.20168.4", "fileVersion": "2.0.20168.4"}}, "compile": {"ref/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {}}}, "Microsoft.Data.SqlClient.SNI.runtime/2.0.1": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.pdb": {"rid": "win-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.pdb": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.pdb": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.pdb": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Microsoft.Data.Sqlite.Core/5.0.17": {"dependencies": {"SQLitePCLRaw.core": "2.0.4"}, "runtime": {"lib/netstandard2.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21206"}}, "compile": {"lib/netstandard2.0/Microsoft.Data.Sqlite.dll": {}}}, "Microsoft.DotNet.PlatformAbstractions/3.1.6": {"runtime": {"lib/netstandard2.0/Microsoft.DotNet.PlatformAbstractions.dll": {"assemblyVersion": "3.1.6.0", "fileVersion": "3.100.620.31604"}}, "compile": {"lib/netstandard2.0/Microsoft.DotNet.PlatformAbstractions.dll": {}}}, "Microsoft.EntityFrameworkCore/5.0.17": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "5.0.17", "Microsoft.EntityFrameworkCore.Analyzers": "5.0.17", "Microsoft.Extensions.Caching.Memory": "5.0.0", "Microsoft.Extensions.DependencyInjection": "5.0.2", "Microsoft.Extensions.Logging": "5.0.0", "System.Collections.Immutable": "5.0.0", "System.ComponentModel.Annotations": "5.0.0", "System.Diagnostics.DiagnosticSource": "5.0.1"}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21206"}}, "compile": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.dll": {}}}, "Microsoft.EntityFrameworkCore.Abstractions/5.0.17": {"runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21206"}}, "compile": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Abstractions.dll": {}}}, "Microsoft.EntityFrameworkCore.Analyzers/5.0.17": {}, "Microsoft.EntityFrameworkCore.Design/5.0.17": {"dependencies": {"Humanizer.Core": "2.8.26", "Microsoft.CSharp": "4.7.0", "Microsoft.EntityFrameworkCore.Relational": "5.0.17"}}, "Microsoft.EntityFrameworkCore.Relational/5.0.17": {"dependencies": {"Microsoft.EntityFrameworkCore": "5.0.17", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21206"}}, "compile": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Relational.dll": {}}}, "Microsoft.EntityFrameworkCore.Sqlite/5.0.17": {"dependencies": {"Microsoft.EntityFrameworkCore.Sqlite.Core": "5.0.17", "SQLitePCLRaw.bundle_e_sqlite3": "2.0.4"}}, "Microsoft.EntityFrameworkCore.Sqlite.Core/5.0.17": {"dependencies": {"Microsoft.Data.Sqlite.Core": "5.0.17", "Microsoft.DotNet.PlatformAbstractions": "3.1.6", "Microsoft.EntityFrameworkCore.Relational": "5.0.17", "Microsoft.Extensions.DependencyModel": "5.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Sqlite.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21206"}}, "compile": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Sqlite.dll": {}}}, "Microsoft.EntityFrameworkCore.SqlServer/5.0.17": {"dependencies": {"Microsoft.Data.SqlClient": "2.0.1", "Microsoft.EntityFrameworkCore.Relational": "5.0.17"}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.SqlServer.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1722.21206"}}, "compile": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.SqlServer.dll": {}}}, "Microsoft.EntityFrameworkCore.Tools/5.0.17": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "5.0.17"}}, "Microsoft.Extensions.ApiDescription.Server/3.0.0": {}, "Microsoft.Extensions.Caching.Abstractions/5.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.Extensions.Caching.Memory/5.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/5.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.Extensions.DependencyInjection/5.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0"}, "runtime": {"lib/net5.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.821.31504"}}, "compile": {"lib/net5.0/Microsoft.Extensions.DependencyInjection.dll": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/5.0.0": {}, "Microsoft.Extensions.DependencyModel/5.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/5.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.Extensions.FileProviders.Embedded/5.0.17": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "5.0.0"}, "runtime": {"lib/net5.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21507"}}, "compile": {"lib/net5.0/Microsoft.Extensions.FileProviders.Embedded.dll": {}}}, "Microsoft.Extensions.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0"}}, "Microsoft.Extensions.Identity.Core/5.0.17": {"dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "5.0.17", "Microsoft.Extensions.Logging": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}, "runtime": {"lib/net5.0/Microsoft.Extensions.Identity.Core.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21507"}}, "compile": {"lib/net5.0/Microsoft.Extensions.Identity.Core.dll": {}}}, "Microsoft.Extensions.Identity.Stores/5.0.17": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "5.0.0", "Microsoft.Extensions.Identity.Core": "5.0.17", "Microsoft.Extensions.Logging": "5.0.0"}, "runtime": {"lib/net5.0/Microsoft.Extensions.Identity.Stores.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21507"}}, "compile": {"lib/net5.0/Microsoft.Extensions.Identity.Stores.dll": {}}}, "Microsoft.Extensions.Logging/5.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "5.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}}, "Microsoft.Extensions.Logging.Abstractions/5.0.0": {}, "Microsoft.Extensions.ObjectPool/2.2.0": {}, "Microsoft.Extensions.Options/5.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.Extensions.Primitives/5.0.0": {}, "Microsoft.Extensions.WebEncoders/2.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "System.Text.Encodings.Web": "4.5.0"}}, "Microsoft.Identity.Client/4.14.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "System.ComponentModel.TypeConverter": "4.3.0", "System.Net.NameResolution": "4.3.0", "System.Private.Uri": "4.3.2", "System.Runtime.Serialization.Formatters": "4.3.0", "System.Runtime.Serialization.Json": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Security.SecureString": "4.3.0", "System.Xml.XDocument": "4.3.0"}, "runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.14.0.0", "fileVersion": "4.14.0.0"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.7.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.7.1"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "6.7.1.0", "fileVersion": "6.7.1.10630"}}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll": {}}}, "Microsoft.IdentityModel.Logging/6.7.1": {"runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "6.7.1.0", "fileVersion": "6.7.1.10630"}}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll": {}}}, "Microsoft.IdentityModel.Protocols/6.7.1": {"dependencies": {"Microsoft.IdentityModel.Logging": "6.7.1", "Microsoft.IdentityModel.Tokens": "6.7.1"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "6.7.1.0", "fileVersion": "6.7.1.10630"}}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll": {}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.7.1": {"dependencies": {"Microsoft.IdentityModel.Protocols": "6.7.1", "System.IdentityModel.Tokens.Jwt": "6.7.1"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "6.7.1.0", "fileVersion": "6.7.1.10630"}}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {}}}, "Microsoft.IdentityModel.Tokens/6.7.1": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Microsoft.IdentityModel.Logging": "6.7.1", "System.Security.Cryptography.Cng": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "6.7.1.0", "fileVersion": "6.7.1.10630"}}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll": {}}}, "Microsoft.Net.Http.Headers/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0", "System.Buffers": "4.5.0"}}, "Microsoft.NETCore.Platforms/3.1.0": {}, "Microsoft.NETCore.Targets/1.1.3": {}, "Microsoft.OpenApi/1.2.3": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.2.3.0", "fileVersion": "1.2.3.0"}}, "compile": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {}}}, "Microsoft.Owin/3.0.1": {"dependencies": {"Owin": "1.0.0"}, "runtime": {"lib/net45/Microsoft.Owin.dll": {"assemblyVersion": "3.0.1.0", "fileVersion": "3.0.40213.64"}}, "compile": {"lib/net45/Microsoft.Owin.dll": {}}}, "Microsoft.Owin.Security/3.0.1": {"dependencies": {"Microsoft.Owin": "3.0.1", "Owin": "1.0.0"}, "runtime": {"lib/net45/Microsoft.Owin.Security.dll": {"assemblyVersion": "3.0.1.0", "fileVersion": "3.0.40213.64"}}, "compile": {"lib/net45/Microsoft.Owin.Security.dll": {}}}, "Microsoft.Owin.Security.Cookies/3.0.1": {"dependencies": {"Microsoft.Owin": "3.0.1", "Microsoft.Owin.Security": "3.0.1", "Owin": "1.0.0"}, "runtime": {"lib/net45/Microsoft.Owin.Security.Cookies.dll": {"assemblyVersion": "3.0.1.0", "fileVersion": "3.0.40213.64"}}, "compile": {"lib/net45/Microsoft.Owin.Security.Cookies.dll": {}}}, "Microsoft.Owin.Security.OAuth/3.0.1": {"dependencies": {"Microsoft.Owin": "3.0.1", "Microsoft.Owin.Security": "3.0.1", "Newtonsoft.Json": "12.0.2", "Owin": "1.0.0"}, "runtime": {"lib/net45/Microsoft.Owin.Security.OAuth.dll": {"assemblyVersion": "3.0.1.0", "fileVersion": "3.0.40213.64"}}, "compile": {"lib/net45/Microsoft.Owin.Security.OAuth.dll": {}}}, "Microsoft.VisualStudio.Web.CodeGeneration/5.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "5.0.2", "Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore": "5.0.2"}, "runtime": {"lib/net5.0/Microsoft.VisualStudio.Web.CodeGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.221.10809"}}, "compile": {"lib/net5.0/Microsoft.VisualStudio.Web.CodeGeneration.dll": {}}}, "Microsoft.VisualStudio.Web.CodeGeneration.Contracts/5.0.2": {"dependencies": {"Newtonsoft.Json": "12.0.2", "System.Collections.Immutable": "5.0.0"}, "runtime": {"lib/net5.0/Microsoft.VisualStudio.Web.CodeGeneration.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.221.10809"}}, "compile": {"lib/net5.0/Microsoft.VisualStudio.Web.CodeGeneration.Contracts.dll": {}}}, "Microsoft.VisualStudio.Web.CodeGeneration.Core/5.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "5.0.2", "Microsoft.VisualStudio.Web.CodeGeneration.Templating": "5.0.2", "Newtonsoft.Json": "12.0.2"}, "runtime": {"lib/net5.0/Microsoft.VisualStudio.Web.CodeGeneration.Core.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.221.10809"}}, "compile": {"lib/net5.0/Microsoft.VisualStudio.Web.CodeGeneration.Core.dll": {}}}, "Microsoft.VisualStudio.Web.CodeGeneration.Design/5.0.2": {"dependencies": {"Microsoft.VisualStudio.Web.CodeGenerators.Mvc": "5.0.2"}, "runtime": {"lib/net5.0/dotnet-aspnet-codegenerator-design.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.221.10809"}}, "runtimeTargets": {"runtimes/win-arm/lib/net5.0/dotnet-aspnet-codegenerator-design.exe": {"rid": "win-arm", "assetType": "runtime", "fileVersion": "5.0.221.10809"}, "runtimes/win-arm64/lib/net5.0/dotnet-aspnet-codegenerator-design.exe": {"rid": "win-arm64", "assetType": "runtime", "fileVersion": "5.0.221.10809"}}, "compile": {"lib/net5.0/dotnet-aspnet-codegenerator-design.dll": {}}}, "Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore/5.0.2": {"dependencies": {"Microsoft.VisualStudio.Web.CodeGeneration.Core": "5.0.2"}, "runtime": {"lib/net5.0/Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.221.10809"}}, "compile": {"lib/net5.0/Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore.dll": {}}}, "Microsoft.VisualStudio.Web.CodeGeneration.Templating/5.0.2": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "5.0.0", "Microsoft.AspNetCore.Razor.Runtime": "2.2.0", "Microsoft.CodeAnalysis.CSharp": "3.8.0", "Microsoft.CodeAnalysis.Razor": "5.0.0", "Microsoft.VisualStudio.Web.CodeGeneration.Utils": "5.0.2"}, "runtime": {"lib/net5.0/Microsoft.VisualStudio.Web.CodeGeneration.Templating.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.221.10809"}}, "compile": {"lib/net5.0/Microsoft.VisualStudio.Web.CodeGeneration.Templating.dll": {}}}, "Microsoft.VisualStudio.Web.CodeGeneration.Utils/5.0.2": {"dependencies": {"Microsoft.CodeAnalysis.CSharp.Workspaces": "3.8.0", "Microsoft.VisualStudio.Web.CodeGeneration.Contracts": "5.0.2", "Newtonsoft.Json": "12.0.2"}, "runtime": {"lib/net5.0/Microsoft.VisualStudio.Web.CodeGeneration.Utils.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.221.10809"}}, "compile": {"lib/net5.0/Microsoft.VisualStudio.Web.CodeGeneration.Utils.dll": {}}}, "Microsoft.VisualStudio.Web.CodeGenerators.Mvc/5.0.2": {"dependencies": {"Microsoft.VisualStudio.Web.CodeGeneration": "5.0.2"}, "runtime": {"lib/net5.0/Microsoft.VisualStudio.Web.CodeGenerators.Mvc.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.221.10809"}}, "compile": {"lib/net5.0/Microsoft.VisualStudio.Web.CodeGenerators.Mvc.dll": {}}}, "Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.0"}}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}}, "Microsoft.Win32.SystemEvents/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0"}}, "MySqlConnector/1.3.13": {"runtime": {"lib/net5.0/MySqlConnector.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}, "compile": {"lib/net5.0/MySqlConnector.dll": {}}}, "NETStandard.Library/1.6.1": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.Win32.Primitives": "4.3.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.Compression.ZipFile": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Net.Http": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Net.Sockets": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Timer": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0"}}, "Newtonsoft.Json/12.0.2": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "12.0.0.0", "fileVersion": "12.0.2.23222"}}, "compile": {"lib/netstandard2.0/Newtonsoft.Json.dll": {}}}, "Newtonsoft.Json.Bson/1.0.1": {"dependencies": {"NETStandard.Library": "1.6.1", "Newtonsoft.Json": "12.0.2"}, "runtime": {"lib/netstandard1.3/Newtonsoft.Json.Bson.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.1.20722"}}, "compile": {"lib/netstandard1.3/Newtonsoft.Json.Bson.dll": {}}}, "Owin/1.0.0": {"runtime": {"lib/net40/Owin.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net40/Owin.dll": {}}}, "Pomelo.EntityFrameworkCore.MySql/5.0.4": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "5.0.17", "Microsoft.Extensions.DependencyInjection": "5.0.2", "MySqlConnector": "1.3.13"}, "runtime": {"lib/netstandard2.1/Pomelo.EntityFrameworkCore.MySql.dll": {"assemblyVersion": "5.0.4.0", "fileVersion": "5.0.4.0"}}, "compile": {"lib/netstandard2.1/Pomelo.EntityFrameworkCore.MySql.dll": {}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "SQLitePCLRaw.bundle_e_sqlite3/2.0.4": {"dependencies": {"SQLitePCLRaw.core": "2.0.4", "SQLitePCLRaw.lib.e_sqlite3": "2.0.4", "SQLitePCLRaw.provider.dynamic_cdecl": "2.0.4"}, "runtime": {"lib/netcoreapp3.1/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.0.4.976", "fileVersion": "2.0.4.976"}, "lib/netcoreapp3.1/SQLitePCLRaw.nativelibrary.dll": {"assemblyVersion": "2.0.4.976", "fileVersion": "2.0.4.976"}}, "compile": {"lib/netcoreapp3.1/SQLitePCLRaw.batteries_v2.dll": {}, "lib/netcoreapp3.1/SQLitePCLRaw.nativelibrary.dll": {}}}, "SQLitePCLRaw.core/2.0.4": {"dependencies": {"System.Memory": "4.5.4"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.0.4.976", "fileVersion": "2.0.4.976"}}, "compile": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {}}}, "SQLitePCLRaw.lib.e_sqlite3/2.0.4": {"runtimeTargets": {"runtimes/alpine-x64/native/libe_sqlite3.so": {"rid": "alpine-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"rid": "linux-armel", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-mips64/native/libe_sqlite3.so": {"rid": "linux-mips64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"rid": "linux-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/e_sqlite3.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/e_sqlite3.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/e_sqlite3.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.provider.dynamic_cdecl/2.0.4": {"dependencies": {"SQLitePCLRaw.core": "2.0.4"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.provider.dynamic_cdecl.dll": {"assemblyVersion": "2.0.4.976", "fileVersion": "2.0.4.976"}}, "compile": {"lib/netstandard2.0/SQLitePCLRaw.provider.dynamic_cdecl.dll": {}}}, "Swashbuckle.AspNetCore/5.6.3": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "3.0.0", "Swashbuckle.AspNetCore.Swagger": "5.6.3", "Swashbuckle.AspNetCore.SwaggerGen": "5.6.3", "Swashbuckle.AspNetCore.SwaggerUI": "5.6.3"}}, "Swashbuckle.AspNetCore.Swagger/5.6.3": {"dependencies": {"Microsoft.OpenApi": "1.2.3"}, "runtime": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.dll": {}}}, "Swashbuckle.AspNetCore.SwaggerGen/5.6.3": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "5.6.3"}, "runtime": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {}}}, "Swashbuckle.AspNetCore.SwaggerUI/5.6.3": {"runtime": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {}}}, "System.AppContext/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Buffers/4.5.0": {}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.0"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable/5.0.0": {}, "System.Collections.NonGeneric/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Collections.Specialized/4.3.0": {"dependencies": {"System.Collections.NonGeneric": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.ComponentModel/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.ComponentModel.Annotations/5.0.0": {}, "System.ComponentModel.Primitives/4.3.0": {"dependencies": {"System.ComponentModel": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.ComponentModel.TypeConverter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.NonGeneric": "4.3.0", "System.Collections.Specialized": "4.3.0", "System.ComponentModel": "4.3.0", "System.ComponentModel.Primitives": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Composition/1.0.31": {"dependencies": {"System.Composition.AttributedModel": "1.0.31", "System.Composition.Convention": "1.0.31", "System.Composition.Hosting": "1.0.31", "System.Composition.Runtime": "1.0.31", "System.Composition.TypedParts": "1.0.31"}}, "System.Composition.AttributedModel/1.0.31": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.Composition.AttributedModel.dll": {"assemblyVersion": "********", "fileVersion": "4.6.24705.1"}}, "compile": {"lib/netstandard1.0/System.Composition.AttributedModel.dll": {}}}, "System.Composition.Convention/1.0.31": {"dependencies": {"System.Collections": "4.3.0", "System.Composition.AttributedModel": "1.0.31", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.Composition.Convention.dll": {"assemblyVersion": "********", "fileVersion": "4.6.24705.1"}}, "compile": {"lib/netstandard1.0/System.Composition.Convention.dll": {}}}, "System.Composition.Hosting/1.0.31": {"dependencies": {"System.Collections": "4.3.0", "System.Composition.Runtime": "1.0.31", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.Composition.Hosting.dll": {"assemblyVersion": "********", "fileVersion": "4.6.24705.1"}}, "compile": {"lib/netstandard1.0/System.Composition.Hosting.dll": {}}}, "System.Composition.Runtime/1.0.31": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.Composition.Runtime.dll": {"assemblyVersion": "********", "fileVersion": "4.6.24705.1"}}, "compile": {"lib/netstandard1.0/System.Composition.Runtime.dll": {}}}, "System.Composition.TypedParts/1.0.31": {"dependencies": {"System.Collections": "4.3.0", "System.Composition.AttributedModel": "1.0.31", "System.Composition.Hosting": "1.0.31", "System.Composition.Runtime": "1.0.31", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.Composition.TypedParts.dll": {"assemblyVersion": "********", "fileVersion": "4.6.24705.1"}}, "compile": {"lib/netstandard1.0/System.Composition.TypedParts.dll": {}}}, "System.Configuration.ConfigurationManager/4.7.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "4.7.0", "System.Security.Permissions": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Console/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/5.0.1": {"runtime": {"lib/net5.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.220.61120"}}, "compile": {"lib/net5.0/System.Diagnostics.DiagnosticSource.dll": {}}}, "System.Diagnostics.Tools/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.0"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.0"}}, "System.Drawing.Common/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.Win32.SystemEvents": "4.7.0"}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.0"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/6.7.1": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.7.1", "Microsoft.IdentityModel.Tokens": "6.7.1"}, "runtime": {"lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "6.7.1.0", "fileVersion": "6.7.1.10630"}}, "compile": {"lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll": {}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Buffers": "4.5.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}}, "System.IO.Compression.ZipFile/4.3.0": {"dependencies": {"System.Buffers": "4.5.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Memory/4.5.4": {}, "System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "5.0.1", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Net.NameResolution/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Principal.Windows": "4.7.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Net.Sockets/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Private.DataContractSerialization/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0", "System.Xml.XmlSerializer": "4.3.0"}}, "System.Private.Uri/4.3.2": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Metadata/5.0.0": {}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3"}}, "System.Runtime.Caching/4.7.0": {"dependencies": {"System.Configuration.ConfigurationManager": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.Runtime.Caching.dll": {"assemblyVersion": "4.0.1.0", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.1.0", "fileVersion": "4.700.19.56404"}}}, "System.Runtime.CompilerServices.Unsafe/4.7.1": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Runtime.Serialization.Formatters/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0"}}, "System.Runtime.Serialization.Json/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Private.DataContractSerialization": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Serialization.Primitives/4.3.0": {"dependencies": {"System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Security.AccessControl/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Cng/4.5.0": {}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Pkcs/4.5.0": {"dependencies": {"System.Security.Cryptography.Cng": "4.5.0"}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData/4.7.0": {"runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "4.0.5.0", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.5.0", "fileVersion": "4.700.19.56404"}}}, "System.Security.Cryptography.X509Certificates/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.5.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Xml/4.5.0": {"dependencies": {"System.Security.Cryptography.Pkcs": "4.5.0", "System.Security.Permissions": "4.7.0"}}, "System.Security.Permissions/4.7.0": {"dependencies": {"System.Security.AccessControl": "4.7.0", "System.Windows.Extensions": "4.7.0"}}, "System.Security.Principal.Windows/4.7.0": {}, "System.Security.SecureString/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0"}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/4.5.0": {}, "System.Text.RegularExpressions/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Threading.Timer/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.0"}}, "System.Windows.Extensions/4.7.0": {"dependencies": {"System.Drawing.Common": "4.7.0"}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.5.4"}}, "System.Xml.XDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XmlSerializer/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}}, "Microsoft.AspNetCore.Antiforgery/*******": {"compile": {"Microsoft.AspNetCore.Antiforgery.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Authentication.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Cookies.Reference/*******": {"compile": {"Microsoft.AspNetCore.Authentication.Cookies.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Core.Reference/*******": {"compile": {"Microsoft.AspNetCore.Authentication.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Reference/*******": {"compile": {"Microsoft.AspNetCore.Authentication.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.OAuth/*******": {"compile": {"Microsoft.AspNetCore.Authentication.OAuth.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authorization/*******": {"compile": {"Microsoft.AspNetCore.Authorization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authorization.Policy/*******": {"compile": {"Microsoft.AspNetCore.Authorization.Policy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Authorization/*******": {"compile": {"Microsoft.AspNetCore.Components.Authorization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components/*******": {"compile": {"Microsoft.AspNetCore.Components.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Forms/*******": {"compile": {"Microsoft.AspNetCore.Components.Forms.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Server/*******": {"compile": {"Microsoft.AspNetCore.Components.Server.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Web/*******": {"compile": {"Microsoft.AspNetCore.Components.Web.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Connections.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Connections.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.CookiePolicy/*******": {"compile": {"Microsoft.AspNetCore.CookiePolicy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cors/*******": {"compile": {"Microsoft.AspNetCore.Cors.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.DataProtection.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection.Reference/*******": {"compile": {"Microsoft.AspNetCore.DataProtection.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection.Extensions/*******": {"compile": {"Microsoft.AspNetCore.DataProtection.Extensions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Diagnostics.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics/*******": {"compile": {"Microsoft.AspNetCore.Diagnostics.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics.HealthChecks/*******": {"compile": {"Microsoft.AspNetCore.Diagnostics.HealthChecks.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore/*******": {"compile": {"Microsoft.AspNetCore.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HostFiltering/*******": {"compile": {"Microsoft.AspNetCore.HostFiltering.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Hosting.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting/*******": {"compile": {"Microsoft.AspNetCore.Hosting.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting.Server.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Html.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Html.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Http.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Connections.Common/*******": {"compile": {"Microsoft.AspNetCore.Http.Connections.Common.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Connections/*******": {"compile": {"Microsoft.AspNetCore.Http.Connections.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Reference/*******": {"compile": {"Microsoft.AspNetCore.Http.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Extensions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Http.Extensions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Features.Reference/*******": {"compile": {"Microsoft.AspNetCore.Http.Features.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpOverrides/*******": {"compile": {"Microsoft.AspNetCore.HttpOverrides.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpsPolicy/*******": {"compile": {"Microsoft.AspNetCore.HttpsPolicy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Identity.Reference/*******": {"compile": {"Microsoft.AspNetCore.Identity.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Localization/*******": {"compile": {"Microsoft.AspNetCore.Localization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Localization.Routing/*******": {"compile": {"Microsoft.AspNetCore.Localization.Routing.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Metadata/*******": {"compile": {"Microsoft.AspNetCore.Metadata.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.ApiExplorer/*******": {"compile": {"Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Core/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Cors/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Cors.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.DataAnnotations/*******": {"compile": {"Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc/*******": {"compile": {"Microsoft.AspNetCore.Mvc.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Formatters.Json/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Formatters.Json.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Formatters.Xml/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Formatters.Xml.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Localization/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Localization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Razor/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Razor.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.RazorPages/*******": {"compile": {"Microsoft.AspNetCore.Mvc.RazorPages.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.TagHelpers/*******": {"compile": {"Microsoft.AspNetCore.Mvc.TagHelpers.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.ViewFeatures/*******": {"compile": {"Microsoft.AspNetCore.Mvc.ViewFeatures.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Razor.Reference/*******": {"compile": {"Microsoft.AspNetCore.Razor.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Razor.Runtime.Reference/*******": {"compile": {"Microsoft.AspNetCore.Razor.Runtime.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCaching/*******": {"compile": {"Microsoft.AspNetCore.ResponseCaching.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCompression/*******": {"compile": {"Microsoft.AspNetCore.ResponseCompression.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Rewrite/*******": {"compile": {"Microsoft.AspNetCore.Rewrite.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Routing.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Routing.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Routing/*******": {"compile": {"Microsoft.AspNetCore.Routing.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.HttpSys/*******": {"compile": {"Microsoft.AspNetCore.Server.HttpSys.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.IIS/*******": {"compile": {"Microsoft.AspNetCore.Server.IIS.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.IISIntegration/*******": {"compile": {"Microsoft.AspNetCore.Server.IISIntegration.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Core/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Session/*******": {"compile": {"Microsoft.AspNetCore.Session.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Common/*******": {"compile": {"Microsoft.AspNetCore.SignalR.Common.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Core/*******": {"compile": {"Microsoft.AspNetCore.SignalR.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR/*******": {"compile": {"Microsoft.AspNetCore.SignalR.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Protocols.Json/*******": {"compile": {"Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.StaticFiles/*******": {"compile": {"Microsoft.AspNetCore.StaticFiles.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.WebSockets/*******": {"compile": {"Microsoft.AspNetCore.WebSockets.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.WebUtilities.Reference/*******": {"compile": {"Microsoft.AspNetCore.WebUtilities.dll": {}}, "compileOnly": true}, "Microsoft.CSharp.Reference/*******": {"compile": {"Microsoft.CSharp.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Caching.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Caching.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Caching.Memory.Reference/*******": {"compile": {"Microsoft.Extensions.Caching.Memory.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Binder/*******": {"compile": {"Microsoft.Extensions.Configuration.Binder.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.CommandLine/*******": {"compile": {"Microsoft.Extensions.Configuration.CommandLine.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration/*******": {"compile": {"Microsoft.Extensions.Configuration.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.EnvironmentVariables/*******": {"compile": {"Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.FileExtensions/*******": {"compile": {"Microsoft.Extensions.Configuration.FileExtensions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Ini/*******": {"compile": {"Microsoft.Extensions.Configuration.Ini.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Json/*******": {"compile": {"Microsoft.Extensions.Configuration.Json.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.KeyPerFile/*******": {"compile": {"Microsoft.Extensions.Configuration.KeyPerFile.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.UserSecrets/*******": {"compile": {"Microsoft.Extensions.Configuration.UserSecrets.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Xml/*******": {"compile": {"Microsoft.Extensions.Configuration.Xml.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.DependencyInjection.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.DependencyInjection.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/*******": {"compile": {"Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.HealthChecks/*******": {"compile": {"Microsoft.Extensions.Diagnostics.HealthChecks.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.FileProviders.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Composite/*******": {"compile": {"Microsoft.Extensions.FileProviders.Composite.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Physical/*******": {"compile": {"Microsoft.Extensions.FileProviders.Physical.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileSystemGlobbing/*******": {"compile": {"Microsoft.Extensions.FileSystemGlobbing.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Hosting.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Hosting.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Hosting/*******": {"compile": {"Microsoft.Extensions.Hosting.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Http/*******": {"compile": {"Microsoft.Extensions.Http.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Localization.Abstractions/*******": {"compile": {"Microsoft.Extensions.Localization.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Localization/*******": {"compile": {"Microsoft.Extensions.Localization.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Logging.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Configuration/*******": {"compile": {"Microsoft.Extensions.Logging.Configuration.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Console/*******": {"compile": {"Microsoft.Extensions.Logging.Console.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Debug/*******": {"compile": {"Microsoft.Extensions.Logging.Debug.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Reference/*******": {"compile": {"Microsoft.Extensions.Logging.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.EventLog/*******": {"compile": {"Microsoft.Extensions.Logging.EventLog.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.EventSource/*******": {"compile": {"Microsoft.Extensions.Logging.EventSource.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.TraceSource/*******": {"compile": {"Microsoft.Extensions.Logging.TraceSource.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.ObjectPool.Reference/*******": {"compile": {"Microsoft.Extensions.ObjectPool.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Options.ConfigurationExtensions/*******": {"compile": {"Microsoft.Extensions.Options.ConfigurationExtensions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Options.DataAnnotations/*******": {"compile": {"Microsoft.Extensions.Options.DataAnnotations.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Options.Reference/*******": {"compile": {"Microsoft.Extensions.Options.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Primitives.Reference/*******": {"compile": {"Microsoft.Extensions.Primitives.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.WebEncoders.Reference/*******": {"compile": {"Microsoft.Extensions.WebEncoders.dll": {}}, "compileOnly": true}, "Microsoft.JSInterop/*******": {"compile": {"Microsoft.JSInterop.dll": {}}, "compileOnly": true}, "Microsoft.Net.Http.Headers.Reference/*******": {"compile": {"Microsoft.Net.Http.Headers.dll": {}}, "compileOnly": true}, "Microsoft.VisualBasic.Core/10.0.6.0": {"compile": {"Microsoft.VisualBasic.Core.dll": {}}, "compileOnly": true}, "Microsoft.VisualBasic/10.0.0.0": {"compile": {"Microsoft.VisualBasic.dll": {}}, "compileOnly": true}, "Microsoft.Win32.Primitives.Reference/*******": {"compile": {"Microsoft.Win32.Primitives.dll": {}}, "compileOnly": true}, "Microsoft.Win32.Registry.Reference/*******": {"compile": {"Microsoft.Win32.Registry.dll": {}}, "compileOnly": true}, "mscorlib/*******": {"compile": {"mscorlib.dll": {}}, "compileOnly": true}, "netstandard/2.1.0.0": {"compile": {"netstandard.dll": {}}, "compileOnly": true}, "System.AppContext.Reference/*******": {"compile": {"System.AppContext.dll": {}}, "compileOnly": true}, "System.Buffers.Reference/*******": {"compile": {"System.Buffers.dll": {}}, "compileOnly": true}, "System.Collections.Concurrent.Reference/*******": {"compile": {"System.Collections.Concurrent.dll": {}}, "compileOnly": true}, "System.Collections.Reference/*******": {"compile": {"System.Collections.dll": {}}, "compileOnly": true}, "System.Collections.Immutable.Reference/*******": {"compile": {"System.Collections.Immutable.dll": {}}, "compileOnly": true}, "System.Collections.NonGeneric.Reference/*******": {"compile": {"System.Collections.NonGeneric.dll": {}}, "compileOnly": true}, "System.Collections.Specialized.Reference/*******": {"compile": {"System.Collections.Specialized.dll": {}}, "compileOnly": true}, "System.ComponentModel.Annotations.Reference/*******": {"compile": {"System.ComponentModel.Annotations.dll": {}}, "compileOnly": true}, "System.ComponentModel.DataAnnotations/*******": {"compile": {"System.ComponentModel.DataAnnotations.dll": {}}, "compileOnly": true}, "System.ComponentModel.Reference/*******": {"compile": {"System.ComponentModel.dll": {}}, "compileOnly": true}, "System.ComponentModel.EventBasedAsync/*******": {"compile": {"System.ComponentModel.EventBasedAsync.dll": {}}, "compileOnly": true}, "System.ComponentModel.Primitives.Reference/*******": {"compile": {"System.ComponentModel.Primitives.dll": {}}, "compileOnly": true}, "System.ComponentModel.TypeConverter.Reference/*******": {"compile": {"System.ComponentModel.TypeConverter.dll": {}}, "compileOnly": true}, "System.Configuration/*******": {"compile": {"System.Configuration.dll": {}}, "compileOnly": true}, "System.Console.Reference/*******": {"compile": {"System.Console.dll": {}}, "compileOnly": true}, "System.Core/*******": {"compile": {"System.Core.dll": {}}, "compileOnly": true}, "System.Data.Common/*******": {"compile": {"System.Data.Common.dll": {}}, "compileOnly": true}, "System.Data.DataSetExtensions/*******": {"compile": {"System.Data.DataSetExtensions.dll": {}}, "compileOnly": true}, "System.Data/*******": {"compile": {"System.Data.dll": {}}, "compileOnly": true}, "System.Diagnostics.Contracts/*******": {"compile": {"System.Diagnostics.Contracts.dll": {}}, "compileOnly": true}, "System.Diagnostics.Debug.Reference/*******": {"compile": {"System.Diagnostics.Debug.dll": {}}, "compileOnly": true}, "System.Diagnostics.EventLog/*******": {"compile": {"System.Diagnostics.EventLog.dll": {}}, "compileOnly": true}, "System.Diagnostics.FileVersionInfo/*******": {"compile": {"System.Diagnostics.FileVersionInfo.dll": {}}, "compileOnly": true}, "System.Diagnostics.Process/*******": {"compile": {"System.Diagnostics.Process.dll": {}}, "compileOnly": true}, "System.Diagnostics.StackTrace/*******": {"compile": {"System.Diagnostics.StackTrace.dll": {}}, "compileOnly": true}, "System.Diagnostics.TextWriterTraceListener/*******": {"compile": {"System.Diagnostics.TextWriterTraceListener.dll": {}}, "compileOnly": true}, "System.Diagnostics.Tools.Reference/*******": {"compile": {"System.Diagnostics.Tools.dll": {}}, "compileOnly": true}, "System.Diagnostics.TraceSource/*******": {"compile": {"System.Diagnostics.TraceSource.dll": {}}, "compileOnly": true}, "System.Diagnostics.Tracing.Reference/*******": {"compile": {"System.Diagnostics.Tracing.dll": {}}, "compileOnly": true}, "System/*******": {"compile": {"System.dll": {}}, "compileOnly": true}, "System.Drawing/*******": {"compile": {"System.Drawing.dll": {}}, "compileOnly": true}, "System.Drawing.Primitives/*******": {"compile": {"System.Drawing.Primitives.dll": {}}, "compileOnly": true}, "System.Dynamic.Runtime/*******": {"compile": {"System.Dynamic.Runtime.dll": {}}, "compileOnly": true}, "System.Formats.Asn1/*******": {"compile": {"System.Formats.Asn1.dll": {}}, "compileOnly": true}, "System.Globalization.Calendars.Reference/*******": {"compile": {"System.Globalization.Calendars.dll": {}}, "compileOnly": true}, "System.Globalization.Reference/*******": {"compile": {"System.Globalization.dll": {}}, "compileOnly": true}, "System.Globalization.Extensions.Reference/*******": {"compile": {"System.Globalization.Extensions.dll": {}}, "compileOnly": true}, "System.IO.Compression.Brotli/*******": {"compile": {"System.IO.Compression.Brotli.dll": {}}, "compileOnly": true}, "System.IO.Compression.Reference/*******": {"compile": {"System.IO.Compression.dll": {}}, "compileOnly": true}, "System.IO.Compression.FileSystem/*******": {"compile": {"System.IO.Compression.FileSystem.dll": {}}, "compileOnly": true}, "System.IO.Compression.ZipFile.Reference/*******": {"compile": {"System.IO.Compression.ZipFile.dll": {}}, "compileOnly": true}, "System.IO.Reference/*******": {"compile": {"System.IO.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.Reference/*******": {"compile": {"System.IO.FileSystem.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.DriveInfo/*******": {"compile": {"System.IO.FileSystem.DriveInfo.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.Primitives.Reference/*******": {"compile": {"System.IO.FileSystem.Primitives.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.Watcher/*******": {"compile": {"System.IO.FileSystem.Watcher.dll": {}}, "compileOnly": true}, "System.IO.IsolatedStorage/*******": {"compile": {"System.IO.IsolatedStorage.dll": {}}, "compileOnly": true}, "System.IO.MemoryMappedFiles/*******": {"compile": {"System.IO.MemoryMappedFiles.dll": {}}, "compileOnly": true}, "System.IO.Pipelines/*******": {"compile": {"System.IO.Pipelines.dll": {}}, "compileOnly": true}, "System.IO.Pipes/*******": {"compile": {"System.IO.Pipes.dll": {}}, "compileOnly": true}, "System.IO.UnmanagedMemoryStream/*******": {"compile": {"System.IO.UnmanagedMemoryStream.dll": {}}, "compileOnly": true}, "System.Linq.Reference/*******": {"compile": {"System.Linq.dll": {}}, "compileOnly": true}, "System.Linq.Expressions.Reference/*******": {"compile": {"System.Linq.Expressions.dll": {}}, "compileOnly": true}, "System.Linq.Parallel/*******": {"compile": {"System.Linq.Parallel.dll": {}}, "compileOnly": true}, "System.Linq.Queryable/*******": {"compile": {"System.Linq.Queryable.dll": {}}, "compileOnly": true}, "System.Memory.Reference/*******": {"compile": {"System.Memory.dll": {}}, "compileOnly": true}, "System.Net/*******": {"compile": {"System.Net.dll": {}}, "compileOnly": true}, "System.Net.Http.Reference/*******": {"compile": {"System.Net.Http.dll": {}}, "compileOnly": true}, "System.Net.Http.Json/*******": {"compile": {"System.Net.Http.Json.dll": {}}, "compileOnly": true}, "System.Net.HttpListener/*******": {"compile": {"System.Net.HttpListener.dll": {}}, "compileOnly": true}, "System.Net.Mail/*******": {"compile": {"System.Net.Mail.dll": {}}, "compileOnly": true}, "System.Net.NameResolution.Reference/*******": {"compile": {"System.Net.NameResolution.dll": {}}, "compileOnly": true}, "System.Net.NetworkInformation/*******": {"compile": {"System.Net.NetworkInformation.dll": {}}, "compileOnly": true}, "System.Net.Ping/*******": {"compile": {"System.Net.Ping.dll": {}}, "compileOnly": true}, "System.Net.Primitives.Reference/*******": {"compile": {"System.Net.Primitives.dll": {}}, "compileOnly": true}, "System.Net.Requests/*******": {"compile": {"System.Net.Requests.dll": {}}, "compileOnly": true}, "System.Net.Security/*******": {"compile": {"System.Net.Security.dll": {}}, "compileOnly": true}, "System.Net.ServicePoint/*******": {"compile": {"System.Net.ServicePoint.dll": {}}, "compileOnly": true}, "System.Net.Sockets.Reference/*******": {"compile": {"System.Net.Sockets.dll": {}}, "compileOnly": true}, "System.Net.WebClient/*******": {"compile": {"System.Net.WebClient.dll": {}}, "compileOnly": true}, "System.Net.WebHeaderCollection/*******": {"compile": {"System.Net.WebHeaderCollection.dll": {}}, "compileOnly": true}, "System.Net.WebProxy/*******": {"compile": {"System.Net.WebProxy.dll": {}}, "compileOnly": true}, "System.Net.WebSockets.Client/*******": {"compile": {"System.Net.WebSockets.Client.dll": {}}, "compileOnly": true}, "System.Net.WebSockets/*******": {"compile": {"System.Net.WebSockets.dll": {}}, "compileOnly": true}, "System.Numerics/*******": {"compile": {"System.Numerics.dll": {}}, "compileOnly": true}, "System.Numerics.Vectors/*******": {"compile": {"System.Numerics.Vectors.dll": {}}, "compileOnly": true}, "System.ObjectModel.Reference/*******": {"compile": {"System.ObjectModel.dll": {}}, "compileOnly": true}, "System.Reflection.DispatchProxy/*******": {"compile": {"System.Reflection.DispatchProxy.dll": {}}, "compileOnly": true}, "System.Reflection.Reference/*******": {"compile": {"System.Reflection.dll": {}}, "compileOnly": true}, "System.Reflection.Emit.Reference/*******": {"compile": {"System.Reflection.Emit.dll": {}}, "compileOnly": true}, "System.Reflection.Emit.ILGeneration.Reference/*******": {"compile": {"System.Reflection.Emit.ILGeneration.dll": {}}, "compileOnly": true}, "System.Reflection.Emit.Lightweight.Reference/*******": {"compile": {"System.Reflection.Emit.Lightweight.dll": {}}, "compileOnly": true}, "System.Reflection.Extensions.Reference/*******": {"compile": {"System.Reflection.Extensions.dll": {}}, "compileOnly": true}, "System.Reflection.Metadata.Reference/*******": {"compile": {"System.Reflection.Metadata.dll": {}}, "compileOnly": true}, "System.Reflection.Primitives.Reference/*******": {"compile": {"System.Reflection.Primitives.dll": {}}, "compileOnly": true}, "System.Reflection.TypeExtensions.Reference/*******": {"compile": {"System.Reflection.TypeExtensions.dll": {}}, "compileOnly": true}, "System.Resources.Reader/*******": {"compile": {"System.Resources.Reader.dll": {}}, "compileOnly": true}, "System.Resources.ResourceManager.Reference/*******": {"compile": {"System.Resources.ResourceManager.dll": {}}, "compileOnly": true}, "System.Resources.Writer/*******": {"compile": {"System.Resources.Writer.dll": {}}, "compileOnly": true}, "System.Runtime.CompilerServices.Unsafe.Reference/*******": {"compile": {"System.Runtime.CompilerServices.Unsafe.dll": {}}, "compileOnly": true}, "System.Runtime.CompilerServices.VisualC/*******": {"compile": {"System.Runtime.CompilerServices.VisualC.dll": {}}, "compileOnly": true}, "System.Runtime.Reference/*******": {"compile": {"System.Runtime.dll": {}}, "compileOnly": true}, "System.Runtime.Extensions.Reference/*******": {"compile": {"System.Runtime.Extensions.dll": {}}, "compileOnly": true}, "System.Runtime.Handles.Reference/*******": {"compile": {"System.Runtime.Handles.dll": {}}, "compileOnly": true}, "System.Runtime.InteropServices.Reference/*******": {"compile": {"System.Runtime.InteropServices.dll": {}}, "compileOnly": true}, "System.Runtime.InteropServices.RuntimeInformation.Reference/*******": {"compile": {"System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "compileOnly": true}, "System.Runtime.Intrinsics/*******": {"compile": {"System.Runtime.Intrinsics.dll": {}}, "compileOnly": true}, "System.Runtime.Loader/*******": {"compile": {"System.Runtime.Loader.dll": {}}, "compileOnly": true}, "System.Runtime.Numerics.Reference/*******": {"compile": {"System.Runtime.Numerics.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization/*******": {"compile": {"System.Runtime.Serialization.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Formatters.Reference/*******": {"compile": {"System.Runtime.Serialization.Formatters.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Json.Reference/*******": {"compile": {"System.Runtime.Serialization.Json.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Primitives.Reference/*******": {"compile": {"System.Runtime.Serialization.Primitives.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Xml/*******": {"compile": {"System.Runtime.Serialization.Xml.dll": {}}, "compileOnly": true}, "System.Security.AccessControl.Reference/*******": {"compile": {"System.Security.AccessControl.dll": {}}, "compileOnly": true}, "System.Security.Claims/*******": {"compile": {"System.Security.Claims.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Algorithms.Reference/*******": {"compile": {"System.Security.Cryptography.Algorithms.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Cng.Reference/*******": {"compile": {"System.Security.Cryptography.Cng.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Csp.Reference/*******": {"compile": {"System.Security.Cryptography.Csp.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Encoding.Reference/*******": {"compile": {"System.Security.Cryptography.Encoding.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Primitives.Reference/*******": {"compile": {"System.Security.Cryptography.Primitives.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.X509Certificates.Reference/*******": {"compile": {"System.Security.Cryptography.X509Certificates.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Xml.Reference/*******": {"compile": {"System.Security.Cryptography.Xml.dll": {}}, "compileOnly": true}, "System.Security/*******": {"compile": {"System.Security.dll": {}}, "compileOnly": true}, "System.Security.Permissions.Reference/*******": {"compile": {"System.Security.Permissions.dll": {}}, "compileOnly": true}, "System.Security.Principal/*******": {"compile": {"System.Security.Principal.dll": {}}, "compileOnly": true}, "System.Security.Principal.Windows.Reference/*******": {"compile": {"System.Security.Principal.Windows.dll": {}}, "compileOnly": true}, "System.Security.SecureString.Reference/*******": {"compile": {"System.Security.SecureString.dll": {}}, "compileOnly": true}, "System.ServiceModel.Web/*******": {"compile": {"System.ServiceModel.Web.dll": {}}, "compileOnly": true}, "System.ServiceProcess/*******": {"compile": {"System.ServiceProcess.dll": {}}, "compileOnly": true}, "System.Text.Encoding.CodePages.Reference/*******": {"compile": {"System.Text.Encoding.CodePages.dll": {}}, "compileOnly": true}, "System.Text.Encoding.Reference/*******": {"compile": {"System.Text.Encoding.dll": {}}, "compileOnly": true}, "System.Text.Encoding.Extensions.Reference/*******": {"compile": {"System.Text.Encoding.Extensions.dll": {}}, "compileOnly": true}, "System.Text.Encodings.Web.Reference/*******": {"compile": {"System.Text.Encodings.Web.dll": {}}, "compileOnly": true}, "System.Text.Json/*******": {"compile": {"System.Text.Json.dll": {}}, "compileOnly": true}, "System.Text.RegularExpressions.Reference/*******": {"compile": {"System.Text.RegularExpressions.dll": {}}, "compileOnly": true}, "System.Threading.Channels/*******": {"compile": {"System.Threading.Channels.dll": {}}, "compileOnly": true}, "System.Threading.Reference/*******": {"compile": {"System.Threading.dll": {}}, "compileOnly": true}, "System.Threading.Overlapped/*******": {"compile": {"System.Threading.Overlapped.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Dataflow/*******": {"compile": {"System.Threading.Tasks.Dataflow.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Reference/*******": {"compile": {"System.Threading.Tasks.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Extensions.Reference/*******": {"compile": {"System.Threading.Tasks.Extensions.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Parallel/*******": {"compile": {"System.Threading.Tasks.Parallel.dll": {}}, "compileOnly": true}, "System.Threading.Thread/*******": {"compile": {"System.Threading.Thread.dll": {}}, "compileOnly": true}, "System.Threading.ThreadPool/*******": {"compile": {"System.Threading.ThreadPool.dll": {}}, "compileOnly": true}, "System.Threading.Timer.Reference/*******": {"compile": {"System.Threading.Timer.dll": {}}, "compileOnly": true}, "System.Transactions/*******": {"compile": {"System.Transactions.dll": {}}, "compileOnly": true}, "System.Transactions.Local/*******": {"compile": {"System.Transactions.Local.dll": {}}, "compileOnly": true}, "System.ValueTuple/*******": {"compile": {"System.ValueTuple.dll": {}}, "compileOnly": true}, "System.Web/*******": {"compile": {"System.Web.dll": {}}, "compileOnly": true}, "System.Web.HttpUtility/*******": {"compile": {"System.Web.HttpUtility.dll": {}}, "compileOnly": true}, "System.Windows/*******": {"compile": {"System.Windows.dll": {}}, "compileOnly": true}, "System.Windows.Extensions.Reference/*******": {"compile": {"System.Windows.Extensions.dll": {}}, "compileOnly": true}, "System.Xml/*******": {"compile": {"System.Xml.dll": {}}, "compileOnly": true}, "System.Xml.Linq/*******": {"compile": {"System.Xml.Linq.dll": {}}, "compileOnly": true}, "System.Xml.ReaderWriter.Reference/*******": {"compile": {"System.Xml.ReaderWriter.dll": {}}, "compileOnly": true}, "System.Xml.Serialization/*******": {"compile": {"System.Xml.Serialization.dll": {}}, "compileOnly": true}, "System.Xml.XDocument.Reference/*******": {"compile": {"System.Xml.XDocument.dll": {}}, "compileOnly": true}, "System.Xml.XmlDocument.Reference/*******": {"compile": {"System.Xml.XmlDocument.dll": {}}, "compileOnly": true}, "System.Xml.XmlSerializer.Reference/*******": {"compile": {"System.Xml.XmlSerializer.dll": {}}, "compileOnly": true}, "System.Xml.XPath/*******": {"compile": {"System.Xml.XPath.dll": {}}, "compileOnly": true}, "System.Xml.XPath.XDocument/*******": {"compile": {"System.Xml.XPath.XDocument.dll": {}}, "compileOnly": true}, "WindowsBase/*******": {"compile": {"WindowsBase.dll": {}}, "compileOnly": true}}}, "libraries": {"mv/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Humanizer.Core/2.8.26": {"type": "package", "serviceable": true, "sha512": "sha512-OiKusGL20vby4uDEswj2IgkdchC1yQ6rwbIkZDVBPIR6al2b7n3pC91elBul9q33KaBgRKhbZH3+2Ur4fnWx2A==", "path": "humanizer.core/2.8.26", "hashPath": "humanizer.core.2.8.26.nupkg.sha512"}, "Microsoft.AspNet.Identity.Core/2.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-Mv5FpoaxspE8hlWLE4r1IdZtovg/OyFgVRhxQbx47J4sqdLImN1hnM1lnwCj+/wLmqtqFk/RYMoZ5GAcwm/00g==", "path": "microsoft.aspnet.identity.core/2.2.3", "hashPath": "microsoft.aspnet.identity.core.2.2.3.nupkg.sha512"}, "Microsoft.AspNet.Identity.Owin/2.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-gNr7dQGE23hAtWmSZVmKPJ+DKBw/obh68Ub7M5cCUiRNAqg6+6Jb3T7en4Fuw8R/8H5/sIsecrfT3MbK2/FzEg==", "path": "microsoft.aspnet.identity.owin/2.2.3", "hashPath": "microsoft.aspnet.identity.owin.2.2.3.nupkg.sha512"}, "Microsoft.AspNet.WebApi.Client/5.2.9": {"type": "package", "serviceable": true, "sha512": "sha512-cuVhPjjNMSEFpKXweMNBbsG4RUFuuZpFBm8tSyw309U9JEjcnbB6n3EPb4xwgcy9bJ38ctIbv5G8zXUBhlrPWw==", "path": "microsoft.aspnet.webapi.client/5.2.9", "hashPath": "microsoft.aspnet.webapi.client.5.2.9.nupkg.sha512"}, "Microsoft.AspNet.WebApi.Core/5.2.9": {"type": "package", "serviceable": true, "sha512": "sha512-9C0/gBUeMBjorRFSmR5Z2j5wznf9BP5SddN+OZE7X0BpXgivBZTZ42d0h8tIjJzSLHYVCU4HZBf51+cidCY4fQ==", "path": "microsoft.aspnet.webapi.core/5.2.9", "hashPath": "microsoft.aspnet.webapi.core.5.2.9.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-b0R9X7L6zMqNsssKDvhYHuNi5x0s4DyHTeXybIAyGaitKiW1Q5aAGKdV2codHPiePv9yHfC9hAMyScXQ/xXhPw==", "path": "microsoft.aspnetcore.authentication/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-VloMLDJMf3n/9ic5lCBOa42IBYJgyB1JhzLsL68Zqg+2bEPWfGBj/xCJy/LrKTArN0coOcZp3wyVTZlx0y9pHQ==", "path": "microsoft.aspnetcore.authentication.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Cookies/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Iar9VFlBHkZGdSG9ZUTmn6Q8Qg+6CtW5G/TyJI2F8B432TOH+nZlkU7O0W0byow6xsxqOYeTviSHz4cCJ3amfQ==", "path": "microsoft.aspnetcore.authentication.cookies/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.cookies.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-XlVJzJ5wPOYW+Y0J6Q/LVTEyfS4ssLXmt60T0SPP+D8abVhBTl+cgw2gDHlyKYIkcJg7btMVh383NDkMVqD/fg==", "path": "microsoft.aspnetcore.authentication.core/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.core.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/5.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-jXMB2+jOG9JvGVCjsKvc5Bo1iSgzdbQw+Iq3BM+eIZ1inDg8rnEVUcWKpRevi9Z6F1/UnSJZ0+rhElT2/aVhhQ==", "path": "microsoft.aspnetcore.authentication.jwtbearer/5.0.17", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.5.0.17.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/5.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-JwAsHM9YfQ7rbtsXZ1iyy0pKOxldRS0Bc7BEEP5kYObGGYOwclW1jRn1aKG1eE2nZoA7vXfJrKrydk07zrv6eg==", "path": "microsoft.aspnetcore.cryptography.internal/5.0.17", "hashPath": "microsoft.aspnetcore.cryptography.internal.5.0.17.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/5.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-Tw460n/TGYOUt2YlNDbxMEZx0aW/+KHkhLVt+0X7h/B0Rfe36lXWrbYpf0/SRd5ztT8olZ7MfPwdkjsuGba62A==", "path": "microsoft.aspnetcore.cryptography.keyderivation/5.0.17", "hashPath": "microsoft.aspnetcore.cryptography.keyderivation.5.0.17.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-G6dvu5Nd2vjpYbzazZ//qBFbSEf2wmBUbyAR7E4AwO3gWjhoJD5YxpThcGJb7oE3VUcW65SVMXT+cPCiiBg8Sg==", "path": "microsoft.aspnetcore.dataprotection/2.2.0", "hashPath": "microsoft.aspnetcore.dataprotection.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-seANFXmp8mb5Y12m1ShiElJ3ZdOT3mBN3wA1GPhHJIvZ/BxOCPyqEOR+810OWsxEZwA5r5fDRNpG/CqiJmQnJg==", "path": "microsoft.aspnetcore.dataprotection.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.dataprotection.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ubycklv+ZY7Kutdwuy1W4upWcZ6VFR8WUXU7l7B2+mvbDBBPAcfpi+E+Y5GFe+Q157YfA3C49D2GCjAZc7Mobw==", "path": "microsoft.aspnetcore.hosting.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-1PMijw8RMtuQF60SsD/JlKtVfvh4NORAhF4wjysdABhlhTrYmtgssqyncR0Stq5vqtjplZcj6kbT4LRTglt9IQ==", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Html.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y4rs5aMEXY8G7wJo5S3EEt6ltqyOTr/qOeZzfn+hw/fuQj5GppGckMY5psGLETo1U9hcT5MmAhaT5xtusM1b5g==", "path": "microsoft.aspnetcore.html.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.html.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-YogBSMotWPAS/X5967pZ+yyWPQkThxhmzAwyCHCSSldzYBkW5W5d6oPfBaPqQOnSHYTpSOSOkpZoAce0vwb6+A==", "path": "microsoft.aspnetcore.http/2.2.0", "hashPath": "microsoft.aspnetcore.http.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-2DgZ9rWrJtuR7RYiew01nGRzuQBDaGHGmK56Rk54vsLLsCdzuFUPqbDTJCS1qJQWTbmbIQ9wGIOjpxA1t0l7/w==", "path": "microsoft.aspnetcore.http.extensions/2.2.0", "hashPath": "microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Identity/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-F16BKeS96wKhyIyhaFR7m8kRIwIvPUW9Dx7IlGWmu2IIwnUDCdo+2z7IrWKA8r77pZQ1UE9kYcBPg5456YdAIA==", "path": "microsoft.aspnetcore.identity/2.2.0", "hashPath": "microsoft.aspnetcore.identity.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/5.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-Ptdhz86gV2XCbEChGX/ji4FQpAgnN/ctaegUQVbWgExLnyEoy2go+8NkYgIfEKUXBVaIewl/HYDeIK6GACKN9Q==", "path": "microsoft.aspnetcore.identity.entityframeworkcore/5.0.17", "hashPath": "microsoft.aspnetcore.identity.entityframeworkcore.5.0.17.nupkg.sha512"}, "Microsoft.AspNetCore.Identity.UI/5.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-10SC96nYr86A3cqpULJYO8W56NhwNX+eTIctsYDzcU/nl53cjIT3psPRrBlF6QChI2stGj5YE2pXTcRSo/22Kg==", "path": "microsoft.aspnetcore.identity.ui/5.0.17", "hashPath": "microsoft.aspnetcore.identity.ui.5.0.17.nupkg.sha512"}, "Microsoft.AspNetCore.JsonPatch/5.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-XSJNa2LNpg8bwHb73jOzh+gmQR+hx+AQb+oWUXDeZPREG5Y1GNJ7ikiBENjfNjys41UVDepPO6I1CqjSV/pOFA==", "path": "microsoft.aspnetcore.jsonpatch/5.0.16", "hashPath": "microsoft.aspnetcore.jsonpatch.5.0.16.nupkg.sha512"}, "Microsoft.AspNetCore.Razor/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-V54PIyDCFl8COnTp9gezNHpUNHk7F9UnerGeZy3UfbnwYvfzbo+ipqQmSgeoESH8e0JvKhRTyQyZquW2EPtCmg==", "path": "microsoft.aspnetcore.razor/2.2.0", "hashPath": "microsoft.aspnetcore.razor.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Language/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6yOBBASGfXMx1fY6hyjvG+oM3eR8vovIehDdEZW7jAV4gKlY4xuAvTm7Iw1fEq7KPunh2VrJwo7oRK1XxUn1OQ==", "path": "microsoft.aspnetcore.razor.language/5.0.0", "hashPath": "microsoft.aspnetcore.razor.language.5.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Runtime/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-7YqK+H61lN6yj9RiQUko7oaOhKtRR9Q/kBcoWNRemhJdTIWOh1OmdvJKzZrMWOlff3BAjejkPQm+0V0qXk+B1w==", "path": "microsoft.aspnetcore.razor.runtime/2.2.0", "hashPath": "microsoft.aspnetcore.razor.runtime.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ErxAAKaDzxXASB/b5uLEkLgUWv1QbeVxyJYEHQwMaxXOeFFVkQxiq8RyfVcifLU7NR0QY0p3acqx4ZpYfhHDg==", "path": "microsoft.aspnetcore.webutilities/2.2.0", "hashPath": "microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w==", "path": "microsoft.bcl.asyncinterfaces/1.1.1", "hashPath": "microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ojG5pGAhTPmjxRGTNvuszO3H8XPZqksDwr9xLd4Ae/JBjZZdl6GuoLk7uLMf+o7yl5wO0TAqoWcEKkEWqrZE5g==", "path": "microsoft.codeanalysis.analyzers/3.0.0", "hashPath": "microsoft.codeanalysis.analyzers.3.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/3.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-8YTZ7GpsbTdC08DITx7/kwV0k4SC6cbBAFqc13cOm5vKJZcEIAh51tNSyGSkWisMgYCr96B2wb5Zri1bsla3+g==", "path": "microsoft.codeanalysis.common/3.8.0", "hashPath": "microsoft.codeanalysis.common.3.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/3.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-hKqFCUSk9TIMBDjiYMF8/ZfK9p9mzpU+slM73CaCHu4ctfkoqJGHLQhyT8wvrYsIg+ufrUWBF8hcJYmyr5rc5Q==", "path": "microsoft.codeanalysis.csharp/3.8.0", "hashPath": "microsoft.codeanalysis.csharp.3.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Workspaces/3.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-rdEBvPWqe/IIscsnp7OkZ4tQin8khxBcSLyV9tU+sHdw9uW9U0GKL+Dv2rD4voC1bZBaO18Hp+m4Vkyfmaz0OA==", "path": "microsoft.codeanalysis.csharp.workspaces/3.8.0", "hashPath": "microsoft.codeanalysis.csharp.workspaces.3.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Razor/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-s4u/6z/MQ35y/egrXf4WgJlUZf5GGvuba9mZ700dH4XxLBrA9Fw9kFZ8uymoATry7hwz5owvFhBVo+2VnoiGRg==", "path": "microsoft.codeanalysis.razor/5.0.0", "hashPath": "microsoft.codeanalysis.razor.5.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.Common/3.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-GPYVydsmOmScOWDJA1LFky7/MkoXpx1JI3lZJShxC+bvVUvL9zVKE8WDZMLsYJ5MAbry2xkZftdfeMpZ+kvLDQ==", "path": "microsoft.codeanalysis.workspaces.common/3.8.0", "hashPath": "microsoft.codeanalysis.workspaces.common.3.8.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-cff+ug/XZnGmX6DFgLY92t7G9W3i8r23w5Qnuby41l9rS+X+f7Y51hV5glvIrmsu3tIcnxbR+Z4CQ2zGhksIJw==", "path": "microsoft.data.sqlclient/2.0.1", "hashPath": "microsoft.data.sqlclient.2.0.1.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-MalWSIMdwLZoNXxjmFmeRrFgaUXbEADkYNGm6HM33pculFv8gKt53s1Frs+kTfVPWMYjocd4gqwz92KrkcLfXA==", "path": "microsoft.data.sqlclient.sni.runtime/2.0.1", "hashPath": "microsoft.data.sqlclient.sni.runtime.2.0.1.nupkg.sha512"}, "Microsoft.Data.Sqlite.Core/5.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-oAMQWz6FNyKI/3c1BOxN1pSqMZnWvkxlheRHh+qsqnmpO5i3R9L1zky4zJ4CyZZJzaOgEWVrK4PMXlnxVl0xmw==", "path": "microsoft.data.sqlite.core/5.0.17", "hashPath": "microsoft.data.sqlite.core.5.0.17.nupkg.sha512"}, "Microsoft.DotNet.PlatformAbstractions/3.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-jek4XYaQ/PGUwDKKhwR8K47Uh1189PFzMeLqO83mXrXQVIpARZCcfuDedH50YDTepBkfijCZN5U/vZi++erxtg==", "path": "microsoft.dotnet.platformabstractions/3.1.6", "hashPath": "microsoft.dotnet.platformabstractions.3.1.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/5.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-cfsvkajdtqQT+aqInu5nRcUPnzdkrS4AtnQONThx0inNTBTxPXZj2/YAc6u3YuYiEidGCL3vxZOwtz3pLM57Ow==", "path": "microsoft.entityframeworkcore/5.0.17", "hashPath": "microsoft.entityframeworkcore.5.0.17.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/5.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-4GpkNWMryNmdrhK9VfgCMTqFjfMRhWUTLalCFrDcInWa90OMESyOU3rQjnYI4ghwTduWjJ2JOSrZbx/G2wv7Wg==", "path": "microsoft.entityframeworkcore.abstractions/5.0.17", "hashPath": "microsoft.entityframeworkcore.abstractions.5.0.17.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/5.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-Gk+AQKXi5KDJgvakivZL1lTrVwidLEBvPg6BLoR0XZcEXytd/SGQKV0YhPAuL0m1+EVp/2DfBbJU/2Ub4Q7NJg==", "path": "microsoft.entityframeworkcore.analyzers/5.0.17", "hashPath": "microsoft.entityframeworkcore.analyzers.5.0.17.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/5.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-DD6dtl3nK+vVAI7c+ip8Wuu8XX4Ou8n/V+dZjf7qybXDh7h2i/BViVBt6nPoEa0oVC9cW1qHLu3iZiyqfbQTuw==", "path": "microsoft.entityframeworkcore.design/5.0.17", "hashPath": "microsoft.entityframeworkcore.design.5.0.17.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/5.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-2N0sbvF7LpTCGG/MHV/Hhvn9d5fPJhOYRNUFhJDA4MMDWvi+bCMMHi0voupR2fYb8EzfMm40kPrmIPz9zbDiaw==", "path": "microsoft.entityframeworkcore.relational/5.0.17", "hashPath": "microsoft.entityframeworkcore.relational.5.0.17.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite/5.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-Q9MUwWrVlPOLVh11cENWQPSCH5h1IUcSBFNMenb/E1EMH47Mf0z0nz5XZOG93uEqqeUjgoAB9TwRaj1VL/vK2g==", "path": "microsoft.entityframeworkcore.sqlite/5.0.17", "hashPath": "microsoft.entityframeworkcore.sqlite.5.0.17.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite.Core/5.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-ceXvWhjwHRVR6c6bQzr4Mej9vED2mC/RLitcd1IYjS0pi+0hZ/xXULbxQyQZ65bkMiQnV4IbZ5iSJJL0j2+dpQ==", "path": "microsoft.entityframeworkcore.sqlite.core/5.0.17", "hashPath": "microsoft.entityframeworkcore.sqlite.core.5.0.17.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.SqlServer/5.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-cvBZZm0CocgXL8je0UYIqNZHK0Zx2oMqNfoHT+GgsEfU2cXkF2PFxP2kOM6jTLtsYiQmR9nBBDOJlO7Nq35fBw==", "path": "microsoft.entityframeworkcore.sqlserver/5.0.17", "hashPath": "microsoft.entityframeworkcore.sqlserver.5.0.17.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/5.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-Wwo0jkprYU6FIm9a+4b6bGT7OQ8Oehn6nECeveofoygi+DN5ft1DmshidoTn6a/5VPzWbzFhkX1bEeW2xSU2+A==", "path": "microsoft.entityframeworkcore.tools/5.0.17", "hashPath": "microsoft.entityframeworkcore.tools.5.0.17.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LH4OE/76F6sOCslif7+Xh3fS/wUUrE5ryeXAMcoCnuwOQGT5Smw0p57IgDh/pHgHaGz/e+AmEQb7pRgb++wt0w==", "path": "microsoft.extensions.apidescription.server/3.0.0", "hashPath": "microsoft.extensions.apidescription.server.3.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bu8As90/SBAouMZ6fJ+qRNo1X+KgHGrVueFhhYi+E5WqEhcnp2HoWRFnMzXQ6g4RdZbvPowFerSbKNH4Dtg5yg==", "path": "microsoft.extensions.caching.abstractions/5.0.0", "hashPath": "microsoft.extensions.caching.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/1qPCleFOkJe0O+xmFqCNLFYQZTJz965sVw8CUB/BQgsApBwzAUsL2BUkDvQW+geRUVTXUS9zLa0pBjC2VJ1gA==", "path": "microsoft.extensions.caching.memory/5.0.0", "hashPath": "microsoft.extensions.caching.memory.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ETjSBHMp3OAZ4HxGQYpwyGsD8Sw5FegQXphi0rpoGMT74S4+I2mm7XJEswwn59XAaKOzC15oDSOWEE8SzDCd6Q==", "path": "microsoft.extensions.configuration.abstractions/5.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/5.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-xzFW00AZEvOXM1OX+0+AYH5op/Hf3u//e6wszBd/rK72sypD+jx5CtsHxM4BVuFBEs8SajfO4QzSJtrQaHDr4A==", "path": "microsoft.extensions.dependencyinjection/5.0.2", "hashPath": "microsoft.extensions.dependencyinjection.5.0.2.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ORj7Zh81gC69TyvmcUm9tSzytcy8AVousi+IVRAI8nLieQjOFryRusSFh7+aLk16FN9pQNqJAiMd7BTKINK0kA==", "path": "microsoft.extensions.dependencyinjection.abstractions/5.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-umBECCoMC+sOUgm083yFr8SxTobUOcPFH4AXigdO2xJiszCHAnmeDl4qPphJt+oaJ/XIfV1wOjIts2nRnki61Q==", "path": "microsoft.extensions.dependencymodel/5.0.0", "hashPath": "microsoft.extensions.dependencymodel.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-iuZIiZ3mteEb+nsUqpGXKx2cGF+cv6gWPd5jqQI4hzqdiJ6I94ddLjKhQOuRW1lueHwocIw30xbSHGhQj0zjdQ==", "path": "microsoft.extensions.fileproviders.abstractions/5.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Embedded/5.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-sSf5oTuE/BYju9hqvwL6CSwArv76mONplyVpYV2J8Il/m2mtuabx2o3YmTeO8aa5+2JaFWZlOX+2X3fWYEp79w==", "path": "microsoft.extensions.fileproviders.embedded/5.0.17", "hashPath": "microsoft.extensions.fileproviders.embedded.5.0.17.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-+k4AEn68HOJat5gj1TWa6X28WlirNQO9sPIIeQbia+91n03esEtMSSoekSTpMjUzjqtJWQN3McVx0GvSPFHF/Q==", "path": "microsoft.extensions.hosting.abstractions/2.2.0", "hashPath": "microsoft.extensions.hosting.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Identity.Core/5.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-JRtQ8YTzuuuinR+/DOLt86apUB4ibm3Oi3JOGV5/rJGsI568VT3jHOxHGiBSXASwuI9j/aC3lPuy7205tX99MQ==", "path": "microsoft.extensions.identity.core/5.0.17", "hashPath": "microsoft.extensions.identity.core.5.0.17.nupkg.sha512"}, "Microsoft.Extensions.Identity.Stores/5.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-h/ZoMcebHf5+eKxYqYiceMzWsRXVV61S0W+V+XCl/XuG+brkkl/Qu4qKAYPn0o9iCHUm4fR62gTRKZOIfc6jog==", "path": "microsoft.extensions.identity.stores/5.0.17", "hashPath": "microsoft.extensions.identity.stores.5.0.17.nupkg.sha512"}, "Microsoft.Extensions.Logging/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MgOwK6tPzB6YNH21wssJcw/2MKwee8b2gI7SllYfn6rvTpIrVvVS5HAjSU2vqSku1fwqRvWP0MdIi14qjd93Aw==", "path": "microsoft.extensions.logging/5.0.0", "hashPath": "microsoft.extensions.logging.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NxP6ahFcBnnSfwNBi2KH2Oz8Xl5Sm2krjId/jRR3I7teFphwiUoUeZPwTNA21EX+5PtjqmyAvKaOeBXcJjcH/w==", "path": "microsoft.extensions.logging.abstractions/5.0.0", "hashPath": "microsoft.extensions.logging.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gA8H7uQOnM5gb+L0uTNjViHYr+hRDqCdfugheGo/MxQnuHzmhhzCBTIPm19qL1z1Xe0NEMabfcOBGv9QghlZ8g==", "path": "microsoft.extensions.objectpool/2.2.0", "hashPath": "microsoft.extensions.objectpool.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Options/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CBvR92TCJ5uBIdd9/HzDSrxYak+0W/3+yxrNg8Qm6Bmrkh5L+nu6m3WeazQehcZ5q1/6dDA7J5YdQjim0165zg==", "path": "microsoft.extensions.options/5.0.0", "hashPath": "microsoft.extensions.options.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cI/VWn9G1fghXrNDagX9nYaaB/nokkZn0HYAawGaELQrl8InSezfe9OnfPZLcJq3esXxygh3hkq2c3qoV3SDyQ==", "path": "microsoft.extensions.primitives/5.0.0", "hashPath": "microsoft.extensions.primitives.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.WebEncoders/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8XcqYcpcdBAxUhLeyYcuKmxu4CtNQA9IphTnARpQGhkop4A93v2XgM3AtaVVJo3H2cDWxWM6aeO8HxkifREqw==", "path": "microsoft.extensions.webencoders/2.2.0", "hashPath": "microsoft.extensions.webencoders.2.2.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.14.0": {"type": "package", "serviceable": true, "sha512": "sha512-Etqux6Zuuv1yEN4UwKbAn6EZv0Rooc+vM4N9z7gxmeT7dyoKlXIRN44DQPzD9LV1CW0KsTVqH+2B42p1NKqPlQ==", "path": "microsoft.identity.client/4.14.0", "hashPath": "microsoft.identity.client.4.14.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/6.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-q/Ii8ILV8cM1X49gnl12cJK+0KWiI1xUeiLYiE9+uRonJLaHWB0l8t89rGnZTEGthGKItyikKSB38LQpfy/zBw==", "path": "microsoft.identitymodel.jsonwebtokens/6.7.1", "hashPath": "microsoft.identitymodel.jsonwebtokens.6.7.1.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/6.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-WGtTiTy2ZikOz/I5GxCGbNPLOpyI9fPyuyG4Q5rfkhACK+Q0Ad6U8XajYZ2cJ2cFKse0IvHwm15HVrfwrX/89g==", "path": "microsoft.identitymodel.logging/6.7.1", "hashPath": "microsoft.identitymodel.logging.6.7.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/6.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-DVGYIRUK3TkCTmz0IgBzWUE55CDNfLtXil1FgSbgHI7hi2fP2pz4tiTAno/5O/hdVwAzV+HtCQtFi7xW8smaHw==", "path": "microsoft.identitymodel.protocols/6.7.1", "hashPath": "microsoft.identitymodel.protocols.6.7.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-99gA+E6ZOCqySrT80Yh6wrfjJfeMxDisdAcA5Q66zHxMPY5Gzc8aT2Ldzu0GP1sADv/o3yI1Gc3P1GHXlXAVVQ==", "path": "microsoft.identitymodel.protocols.openidconnect/6.7.1", "hashPath": "microsoft.identitymodel.protocols.openidconnect.6.7.1.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/6.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-Td9Vn9d/0eM1zlUUvaVQzjqdBkBLJ2oGtGL/LYPuiCUAALMeAHVDtpXGk8eYI8Gbduz5n+o7ifldsCIca4MWew==", "path": "microsoft.identitymodel.tokens/6.7.1", "hashPath": "microsoft.identitymodel.tokens.6.7.1.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iZNkjYqlo8sIOI0bQfpsSoMTmB/kyvmV2h225ihyZT33aTp48ZpF6qYnXxzSXmHt8DpBAwBTX+1s1UFLbYfZKg==", "path": "microsoft.net.http.headers/2.2.0", "hashPath": "microsoft.net.http.headers.2.2.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "path": "microsoft.netcore.platforms/3.1.0", "hashPath": "microsoft.netcore.platforms.3.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-3Wrmi0kJDzClwAC+iBdUBpEKmEle8FQNsCs77fkiOIw/9oYA07bL1EZNX0kQ2OMN3xpwvl0vAtOCYY3ndDNlhQ==", "path": "microsoft.netcore.targets/1.1.3", "hashPath": "microsoft.netcore.targets.1.1.3.nupkg.sha512"}, "Microsoft.OpenApi/1.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-Nug3rO+7Kl5/SBAadzSMAVgqDlfGjJZ0GenQrLywJ84XGKO0uRqkunz5Wyl0SDwcR71bAATXvSdbdzPrYRYKGw==", "path": "microsoft.openapi/1.2.3", "hashPath": "microsoft.openapi.1.2.3.nupkg.sha512"}, "Microsoft.Owin/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-64yZsGN9uRnXNVAyAT4F6KWmdDUJ6yX4uqzxKTdGANKH1oMt0IKZ7THRRa3mXIOo8iSu4tq/DCBUycUY9pXZzw==", "path": "microsoft.owin/3.0.1", "hashPath": "microsoft.owin.3.0.1.nupkg.sha512"}, "Microsoft.Owin.Security/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-LPoUxaTKzmjs97wDCTtBIoRwp30Jkq9UFnoj+EfMFJv+XxjcvHE/D573LRomA568WhvXoI2cUiwhZFvqfbLMVg==", "path": "microsoft.owin.security/3.0.1", "hashPath": "microsoft.owin.security.3.0.1.nupkg.sha512"}, "Microsoft.Owin.Security.Cookies/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-l2GRNvO4oyxfpmWtfUYvN3fA4jWduE84UCNqWTj6yhYDSE76wI+W022qTTEMr0VoCDiJ0GGYdvAjQm3xbSsB9Q==", "path": "microsoft.owin.security.cookies/3.0.1", "hashPath": "microsoft.owin.security.cookies.3.0.1.nupkg.sha512"}, "Microsoft.Owin.Security.OAuth/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-wttlhNSijhsrF7iwxYzxhTr8AYxXI6qoiw3b6dhVhqXOYCDp8OQ5yhHFtWlst+65l7VHduXassspZ974rtLMaw==", "path": "microsoft.owin.security.oauth/3.0.1", "hashPath": "microsoft.owin.security.oauth.3.0.1.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration/5.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-YUah81QG5q/ViVbr1BZcTbDLNJ5/k84fr+xx3/IoDVJR8KEUm89HmPAGM+FMMyWOjit+CIVpyOq7yEmRBBWXxQ==", "path": "microsoft.visualstudio.web.codegeneration/5.0.2", "hashPath": "microsoft.visualstudio.web.codegeneration.5.0.2.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration.Contracts/5.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-34v6AkkRJykgFq7rHwNbzXBsLFquevLuegM9XDQl2j+wyOfj+ql1++jUR1WdZoPkv04WoM09mD47S3lMzJmHrQ==", "path": "microsoft.visualstudio.web.codegeneration.contracts/5.0.2", "hashPath": "microsoft.visualstudio.web.codegeneration.contracts.5.0.2.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration.Core/5.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-R7mrxvTtv/MiEH42OtHYi/3L0A/vaAH8mwg+3yAyQtVuy6v9CeeVyL30lfTQ7EYV4ezUmuQKFwfjcU6PP0/KSQ==", "path": "microsoft.visualstudio.web.codegeneration.core/5.0.2", "hashPath": "microsoft.visualstudio.web.codegeneration.core.5.0.2.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration.Design/5.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-9eTZV7W+S2iO2AJD03xXyXJZ+Nf71Y25gMXhqyXb8bB63jPfn+VQhV8I1lb6J+NR3jW98m5EB9QBftBSrjgiYQ==", "path": "microsoft.visualstudio.web.codegeneration.design/5.0.2", "hashPath": "microsoft.visualstudio.web.codegeneration.design.5.0.2.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore/5.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-f9XeBRS9ICosrCpbO9jnAVMd/ISLhaZgx388XNBjigiyBJuq577J6tQgQWZA8PQTiPj6MKe9HVIW2GnKXDiUrQ==", "path": "microsoft.visualstudio.web.codegeneration.entityframeworkcore/5.0.2", "hashPath": "microsoft.visualstudio.web.codegeneration.entityframeworkcore.5.0.2.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration.Templating/5.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-P3z/JZTGP5DhSc8ik4xrimWuCZ2ZaEZ6q7WGgfgmSVibfXxwh2Oo+dtdkiXwq8MNlkrcP0AZAo3+1wowYUzluA==", "path": "microsoft.visualstudio.web.codegeneration.templating/5.0.2", "hashPath": "microsoft.visualstudio.web.codegeneration.templating.5.0.2.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration.Utils/5.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-4zViWGIFeKsGxDmc5xpn2G8kWs2FSHiLOolw85ZPHihDXc2jiFKp7qjA3SRt8U23kR3zeb0vZiFlETxgTHwAUA==", "path": "microsoft.visualstudio.web.codegeneration.utils/5.0.2", "hashPath": "microsoft.visualstudio.web.codegeneration.utils.5.0.2.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGenerators.Mvc/5.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-W4Uk2y0oja+4E+XP5d5OFu+ViTEtlqm3a6nYuuC3tjA+lTK6dLaMf0G6WnO4BO18i0kM0l49XjTwwXd5XpjnAQ==", "path": "microsoft.visualstudio.web.codegenerators.mvc/5.0.2", "hashPath": "microsoft.visualstudio.web.codegenerators.mvc.5.0.2.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "path": "microsoft.win32.primitives/4.3.0", "hashPath": "microsoft.win32.primitives.4.3.0.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-mtVirZr++rq+XCDITMUdnETD59XoeMxSpLRIII7JRI6Yj0LEDiO1pPn0ktlnIj12Ix8bfvQqQDMMIF9wC98oCA==", "path": "microsoft.win32.systemevents/4.7.0", "hashPath": "microsoft.win32.systemevents.4.7.0.nupkg.sha512"}, "MySqlConnector/1.3.13": {"type": "package", "serviceable": true, "sha512": "sha512-o1qsijkFGvTslpuQdcJwG6IvdNOiVL7fRqvFZ0LTg04kfz+IzzwSWj4YwgTe0q0C3p0WqgaDXui5OEx6qgwebg==", "path": "mysqlconnector/1.3.13", "hashPath": "mysqlconnector.1.3.13.nupkg.sha512"}, "NETStandard.Library/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-WcSp3+vP+yHNgS8EV5J7pZ9IRpeDuARBPN28by8zqff1wJQXm26PVU8L3/fYLBJVU7BtDyqNVWq2KlCVvSSR4A==", "path": "netstandard.library/1.6.1", "hashPath": "netstandard.library.1.6.1.nupkg.sha512"}, "Newtonsoft.Json/12.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-rTK0s2EKlfHsQsH6Yx2smvcTCeyoDNgCW7FEYyV01drPlh2T243PR2DiDXqtC5N4GDm4Ma/lkxfW5a/4793vbA==", "path": "newtonsoft.json/12.0.2", "hashPath": "newtonsoft.json.12.0.2.nupkg.sha512"}, "Newtonsoft.Json.Bson/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5PYT/IqQ+UK31AmZiSS102R6EsTo+LGTSI8bp7WAUqDKaF4wHXD8U9u4WxTI1vc64tYi++8p3dk3WWNqPFgldw==", "path": "newtonsoft.json.bson/1.0.1", "hashPath": "newtonsoft.json.bson.1.0.1.nupkg.sha512"}, "Owin/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OseTFniKmyp76mEzOBwIKGBRS5eMoYNkMKaMXOpxx9jv88+b6mh1rSaw43vjBOItNhaLFG3d0a20PfHyibH5sw==", "path": "owin/1.0.0", "hashPath": "owin.1.0.0.nupkg.sha512"}, "Pomelo.EntityFrameworkCore.MySql/5.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-R5LDFDaCO/Si09tEWbgQoYwLsCYPG3Qm6Q7zICFDF8zZ7gr64fgyBLBtOxhGtJBmbpImFVI6GfcYf2JAROGaZA==", "path": "pomelo.entityframeworkcore.mysql/5.0.4", "hashPath": "pomelo.entityframeworkcore.mysql.5.0.4.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-HdSSp5MnJSsg08KMfZThpuLPJpPwE5hBXvHwoKWosyHHfe8Mh5WKT0ylEOf6yNzX6Ngjxe4Whkafh5q7Ymac4Q==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+yH1a49wJMy8Zt4yx5RhJrxO/DBDByAiCzNwiETI+1S4mPdCu0OY4djdciC7Vssk0l22wQaDLrXxXkp+3+7bVA==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c3YNH1GQJbfIPJeCnr4avseugSqPrxwIqzthYyZDN6EuOyNOzq+y2KSUfRcXauya1sF4foESTgwM5e1A8arAKw==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "path": "runtime.native.system.io.compression/4.3.0", "hashPath": "runtime.native.system.io.compression.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NS1U+700m4KFRHR5o4vo9DSlTmlCKu/u7dtE5sUHVIPB+xpXxYQvgBgA6wEIeCz6Yfn0Z52/72WYsToCEPJnrw==", "path": "runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-b3pthNgxxFcD+Pc0WSEoC0+md3MyhRS6aCEeenvNE3Fdw1HyJ18ZhRFVJJzIeR/O/jpxPboB805Ho0T3Ul7w8A==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KeLz4HClKf+nFS7p/6Fi/CqyLXh81FpiGzcmuS8DGi9lUqSnZ6Es23/gv2O+1XVGfrbNmviF7CckBpavkBoIFQ==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X7IdhILzr4ROXd8mI1BUCQMSHSQwelUlBjF1JyTKCjXaOGn2fB4EKBxQbCK2VjO3WaWIdlXZL3W6TiIVnrhX4g==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-nyFNiCk/r+VOiIqreLix8yN+q3Wga9+SE8BCgkf+2BwEKiNx6DyvFjCgkfV743/grxv8jHJ8gUK4XEQw7yzRYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ytoewC6wGorL7KoCAvRfsgoJPJbNq+64k2SqW6JcOAebWsFUvCCYgfzQMrnpvPiEl4OrblUlhF2ji+Q1+SVLrQ==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I8bKw2I8k58Wx7fMKQJn2R8lamboCAiHfHeV/pS65ScKWMMI0+wJkLYlEKvgW1D/XvSl/221clBoR2q9QNNM7A==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VB5cn/7OzUfzdnC8tqAIMQciVLiq2epm2NrAm1E9OjNRyG4lVhfR61SMcLizejzQP8R8Uf/0l5qOIbUEi+RdEg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "SQLitePCLRaw.bundle_e_sqlite3/2.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-f5U8Sw0lRym8tTraJ2zm6OqcDrcrEVvcKDtYlKSLs3Ox9SerkwkPXiFXb/uiW0g2tJdUw6oBhsxI/l5DoRxXMg==", "path": "sqlitepclraw.bundle_e_sqlite3/2.0.4", "hashPath": "sqlitepclraw.bundle_e_sqlite3.2.0.4.nupkg.sha512"}, "SQLitePCLRaw.core/2.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-4XlDZpDAsboMD6qZQcz9AaKblKDUTVHF+8f3lvbP7QjoqSRr2Xc0Lm34IK2pjRIYnyFLhI3yOJ5YWfOiCid2yg==", "path": "sqlitepclraw.core/2.0.4", "hashPath": "sqlitepclraw.core.2.0.4.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-oetvmtDZOE4Nnrtxd8Trapl9geBiu0rDCUXff46qGYjnUwzaU1mZ3OHnfR402tl32rx8gBWg3n5OBRaPJRbsGw==", "path": "sqlitepclraw.lib.e_sqlite3/2.0.4", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.0.4.nupkg.sha512"}, "SQLitePCLRaw.provider.dynamic_cdecl/2.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-AY6+vv/4ji1mCkLrS6HP/88rHT9YFKRyg3LUj8RyIk6imJMUFdQDiP8rK8gq0a/0FbqspLjK1t7rtKcr7FXRYA==", "path": "sqlitepclraw.provider.dynamic_cdecl/2.0.4", "hashPath": "sqlitepclraw.provider.dynamic_cdecl.2.0.4.nupkg.sha512"}, "Swashbuckle.AspNetCore/5.6.3": {"type": "package", "serviceable": true, "sha512": "sha512-UkL9GU0mfaA+7RwYjEaBFvAzL8qNQhNqAeV5uaWUu/Z+fVgvK9FHkGCpTXBqSQeIHuZaIElzxnLDdIqGzuCnVg==", "path": "swashbuckle.aspnetcore/5.6.3", "hashPath": "swashbuckle.aspnetcore.5.6.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/5.6.3": {"type": "package", "serviceable": true, "sha512": "sha512-rn/MmLscjg6WSnTZabojx5DQYle2GjPanSPbCU3Kw8Hy72KyQR3uy8R1Aew5vpNALjfUFm2M/vwUtqdOlzw+GA==", "path": "swashbuckle.aspnetcore.swagger/5.6.3", "hashPath": "swashbuckle.aspnetcore.swagger.5.6.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/5.6.3": {"type": "package", "serviceable": true, "sha512": "sha512-CkhVeod/iLd3ikVTDOwG5sym8BE5xbqGJ15iF3cC7ZPg2kEwDQL4a88xjkzsvC9oOB2ax6B0rK0EgRK+eOBX+w==", "path": "swashbuckle.aspnetcore.swaggergen/5.6.3", "hashPath": "swashbuckle.aspnetcore.swaggergen.5.6.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/5.6.3": {"type": "package", "serviceable": true, "sha512": "sha512-BPvcPxQRMsYZ3HnYmGKRWDwX4Wo29WHh14Q6B10BB8Yfbbcza+agOC2UrBFA1EuaZuOsFLbp6E2+mqVNF/Je8A==", "path": "swashbuckle.aspnetcore.swaggerui/5.6.3", "hashPath": "swashbuckle.aspnetcore.swaggerui.5.6.3.nupkg.sha512"}, "System.AppContext/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKC+rmaLfeIzUhagxY17Q9siv/sPrjjKcfNg1Ic8IlQkZLipo8ljcaZQu4VtI4Jqbzjc2VTjzGLF6WmsRXAEgA==", "path": "system.appcontext/4.3.0", "hashPath": "system.appcontext.4.3.0.nupkg.sha512"}, "System.Buffers/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A==", "path": "system.buffers/4.5.0", "hashPath": "system.buffers.4.5.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FXkLXiK0sVVewcso0imKQoOxjoPAj42R8HtjjbSjVPAzwDfzoyoznWxgA3c38LDbN9SJux1xXoXYAhz98j7r2g==", "path": "system.collections.immutable/5.0.0", "hashPath": "system.collections.immutable.5.0.0.nupkg.sha512"}, "System.Collections.NonGeneric/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-prtjIEMhGUnQq6RnPEYLpFt8AtLbp9yq2zxOSrY7KJJZrw25Fi97IzBqY7iqssbM61Ek5b8f3MG/sG1N2sN5KA==", "path": "system.collections.nongeneric/4.3.0", "hashPath": "system.collections.nongeneric.4.3.0.nupkg.sha512"}, "System.Collections.Specialized/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Epx8PoVZR0iuOnJJDzp7pWvdfMMOAvpUo95pC4ScH2mJuXkKA2Y4aR3cG9qt2klHgSons1WFh4kcGW7cSXvrxg==", "path": "system.collections.specialized/4.3.0", "hashPath": "system.collections.specialized.4.3.0.nupkg.sha512"}, "System.ComponentModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyGn1jGRZVfxnh8EdvDCi71v3bMXrsu8aYJOwoV7SNDLVhiEqwP86pPMyRGsDsxhXAm2b3o9OIqeETfN5qfezw==", "path": "system.componentmodel/4.3.0", "hashPath": "system.componentmodel.4.3.0.nupkg.sha512"}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "path": "system.componentmodel.annotations/5.0.0", "hashPath": "system.componentmodel.annotations.5.0.0.nupkg.sha512"}, "System.ComponentModel.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-j8GUkCpM8V4d4vhLIIoBLGey2Z5bCkMVNjEZseyAlm4n5arcsJOeI3zkUP+zvZgzsbLTYh4lYeP/ZD/gdIAPrw==", "path": "system.componentmodel.primitives/4.3.0", "hashPath": "system.componentmodel.primitives.4.3.0.nupkg.sha512"}, "System.ComponentModel.TypeConverter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-16pQ6P+EdhcXzPiEK4kbA953Fu0MNG2ovxTZU81/qsCd1zPRsKc3uif5NgvllCY598k6bI0KUyKW8fanlfaDQg==", "path": "system.componentmodel.typeconverter/4.3.0", "hashPath": "system.componentmodel.typeconverter.4.3.0.nupkg.sha512"}, "System.Composition/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-I+D26qpYdoklyAVUdqwUBrEIckMNjAYnuPJy/h9dsQItpQwVREkDFs4b4tkBza0kT2Yk48Lcfsv2QQ9hWsh9Iw==", "path": "system.composition/1.0.31", "hashPath": "system.composition.1.0.31.nupkg.sha512"}, "System.Composition.AttributedModel/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-NHWhkM3ZkspmA0XJEsKdtTt1ViDYuojgSND3yHhTzwxepiwqZf+BCWuvCbjUt4fe0NxxQhUDGJ5km6sLjo9qnQ==", "path": "system.composition.attributedmodel/1.0.31", "hashPath": "system.composition.attributedmodel.1.0.31.nupkg.sha512"}, "System.Composition.Convention/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-GLjh2Ju71k6C0qxMMtl4efHa68NmWeIUYh4fkUI8xbjQrEBvFmRwMDFcylT8/PR9SQbeeL48IkFxU/+gd0nYEQ==", "path": "system.composition.convention/1.0.31", "hashPath": "system.composition.convention.1.0.31.nupkg.sha512"}, "System.Composition.Hosting/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-fN1bT4RX4vUqjbgoyuJFVUizAl2mYF5VAb+bVIxIYZSSc0BdnX+yGAxcavxJuDDCQ1K+/mdpgyEFc8e9ikjvrg==", "path": "system.composition.hosting/1.0.31", "hashPath": "system.composition.hosting.1.0.31.nupkg.sha512"}, "System.Composition.Runtime/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-0LEJN+2NVM89CE4SekDrrk5tHV5LeATltkp+9WNYrR+Huiyt0vaCqHbbHtVAjPyeLWIc8dOz/3kthRBj32wGQg==", "path": "system.composition.runtime/1.0.31", "hashPath": "system.composition.runtime.1.0.31.nupkg.sha512"}, "System.Composition.TypedParts/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-0Zae/FtzeFgDBBuILeIbC/T9HMYbW4olAmi8XqqAGosSOWvXfiQLfARZEhiGd0LVXaYgXr0NhxiU1LldRP1fpQ==", "path": "system.composition.typedparts/1.0.31", "hashPath": "system.composition.typedparts.1.0.31.nupkg.sha512"}, "System.Configuration.ConfigurationManager/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-/anOTeSZCNNI2zDilogWrZ8pNqCmYbzGNexUnNhjW8k0sHqEZ2nHJBp147jBV3hGYswu5lINpNg1vxR7bnqvVA==", "path": "system.configuration.configurationmanager/4.7.0", "hashPath": "system.configuration.configurationmanager.4.7.0.nupkg.sha512"}, "System.Console/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHDrIxiqk1h03m6khKWV2X8p/uvN79rgSqpilL6uzpmSfxfU5ng8VcPtW4qsDsQDHiTv6IPV9TmD5M/vElPNLg==", "path": "system.console/4.3.0", "hashPath": "system.console.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uXQEYqav2V3zP6OwkOKtLv+qIi6z3m1hsGyKwXX7ZA7htT4shoVccGxnJ9kVRFPNAsi1ArZTq2oh7WOto6GbkQ==", "path": "system.diagnostics.diagnosticsource/5.0.1", "hashPath": "system.diagnostics.diagnosticsource.5.0.1.nupkg.sha512"}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "path": "system.diagnostics.tools/4.3.0", "hashPath": "system.diagnostics.tools.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Drawing.Common/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-v+XbyYHaZjDfn0ENmJEV1VYLgGgCTx1gnfOBcppowbpOAriglYgGCvFCPr2EEZyBvXlpxbEsTwkOlInl107ahA==", "path": "system.drawing.common/4.7.0", "hashPath": "system.drawing.common.4.7.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-sPnRn9dUMYARQC3mAKWpig/7rlrruqJvopKXmGoYAQ1A+xQsT3q5LiwsArkV8Oz/hfiRCLkV9vgi3FQg/mYfrw==", "path": "system.identitymodel.tokens.jwt/6.7.1", "hashPath": "system.identitymodel.tokens.jwt.6.7.1.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "path": "system.io.compression/4.3.0", "hashPath": "system.io.compression.4.3.0.nupkg.sha512"}, "System.IO.Compression.ZipFile/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-G4HwjEsgIwy3JFBduZ9quBkAu+eUwjIdJleuNSgmUojbH6O3mlvEIme+GHx/cLlTAPcrnnL7GqvB9pTlWRfhOg==", "path": "system.io.compression.zipfile/4.3.0", "hashPath": "system.io.compression.zipfile.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-sYg+FtILtRQuYWSIAuNOELwVuVsxVyJGWQyOnlAzhV4xvhyFnON1bAzYYC+jjRW8JREM45R0R5Dgi8MTC5sEwA==", "path": "system.net.http/4.3.0", "hashPath": "system.net.http.4.3.0.nupkg.sha512"}, "System.Net.NameResolution/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-AFYl08R7MrsrEjqpQWTZWBadqXyTzNDaWpMqyxhb0d6sGhV6xMDKueuBXlLL30gz+DIRY6MpdgnHWlCh5wmq9w==", "path": "system.net.nameresolution/4.3.0", "hashPath": "system.net.nameresolution.4.3.0.nupkg.sha512"}, "System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "path": "system.net.primitives/4.3.0", "hashPath": "system.net.primitives.4.3.0.nupkg.sha512"}, "System.Net.Sockets/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-m6icV6TqQOAdgt5N/9I5KNpjom/5NFtkmGseEH+AK/hny8XrytLH3+b5M8zL/Ycg3fhIocFpUMyl/wpFnVRvdw==", "path": "system.net.sockets/4.3.0", "hashPath": "system.net.sockets.4.3.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Private.DataContractSerialization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yDaJ2x3mMmjdZEDB4IbezSnCsnjQ4BxinKhRAaP6kEgL6Bb6jANWphs5SzyD8imqeC/3FxgsuXT6ykkiH1uUmA==", "path": "system.private.datacontractserialization/4.3.0", "hashPath": "system.private.datacontractserialization.4.3.0.nupkg.sha512"}, "System.Private.Uri/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-o1+7RJnu3Ik3PazR7Z7tJhjPdE000Eq2KGLLWhqJJKXj04wrS8lwb1OFtDF9jzXXADhUuZNJZlPc98uwwqmpFA==", "path": "system.private.uri/4.3.2", "hashPath": "system.private.uri.4.3.2.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-5NecZgXktdGg34rh1OenY1rFNDCI8xSjFr+Z4OU4cU06AQHUdRnIIEeWENu3Wl4YowbzkymAIMvi3WyK9U53pQ==", "path": "system.reflection.metadata/5.0.0", "hashPath": "system.reflection.metadata.5.0.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Caching/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-NdvNRjTPxYvIEhXQszT9L9vJhdQoX6AQ0AlhjTU+5NqFQVuacJTfhPVAvtGWNA2OJCqRiR/okBcZgMwI6MqcZg==", "path": "system.runtime.caching/4.7.0", "hashPath": "system.runtime.caching.4.7.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/4.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-zOHkQmzPCn5zm/BH+cxC1XbUS3P4Yoi3xzW7eRgVpDR2tPGSzyMZ17Ig1iRkfJuY0nhxkQQde8pgePNiA7z7TQ==", "path": "system.runtime.compilerservices.unsafe/4.7.1", "hashPath": "system.runtime.compilerservices.unsafe.4.7.1.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Formatters/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KT591AkTNFOTbhZlaeMVvfax3RqhH1EJlcwF50Wm7sfnBLuHiOeZRRKrr1ns3NESkM20KPZ5Ol/ueMq5vg4QoQ==", "path": "system.runtime.serialization.formatters/4.3.0", "hashPath": "system.runtime.serialization.formatters.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Json/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-CpVfOH0M/uZ5PH+M9+Gu56K0j9lJw3M+PKRegTkcrY/stOIvRUeonggxNrfBYLA5WOHL2j15KNJuTuld3x4o9w==", "path": "system.runtime.serialization.json/4.3.0", "hashPath": "system.runtime.serialization.json.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wz+0KOukJGAlXjtKr+5Xpuxf8+c8739RI1C+A2BoQZT+wMCCoMDDdO8/4IRHfaVINqL78GO8dW8G2lW/e45Mcw==", "path": "system.runtime.serialization.primitives/4.3.0", "hashPath": "system.runtime.serialization.primitives.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "path": "system.security.accesscontrol/4.7.0", "hashPath": "system.security.accesscontrol.4.7.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-WG3r7EyjUe9CMPFSs6bty5doUqT+q9pbI80hlNzo2SkPkZ4VTuZkGWjpp77JB8+uaL4DFPRdBsAY+DX3dBK92A==", "path": "system.security.cryptography.cng/4.5.0", "hashPath": "system.security.cryptography.cng.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-TGQX51gxpY3K3I6LJlE2LAftVlIMqJf0cBGhz68Y89jjk3LJCB6SrwiD+YN1fkqemBvWGs+GjyMJukl6d6goyQ==", "path": "system.security.cryptography.pkcs/4.5.0", "hashPath": "system.security.cryptography.pkcs.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ehYW0m9ptxpGWvE4zgqongBVWpSDU/JCFD4K7krxkQwSz/sFQjEXCUqpvencjy6DYDbn7Ig09R8GFffu8TtneQ==", "path": "system.security.cryptography.protecteddata/4.7.0", "hashPath": "system.security.cryptography.protecteddata.4.7.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "path": "system.security.cryptography.x509certificates/4.3.0", "hashPath": "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-i2Jn6rGXR63J0zIklImGRkDIJL4b1NfPSEbIVHBlqoIb12lfXIigCbDRpDmIEzwSo/v1U5y/rYJdzZYSyCWxvg==", "path": "system.security.cryptography.xml/4.5.0", "hashPath": "system.security.cryptography.xml.4.5.0.nupkg.sha512"}, "System.Security.Permissions/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-dkOV6YYVBnYRa15/yv004eCGRBVADXw8qRbbNiCn/XpdJSUXkkUeIvdvFHkvnko4CdKMqG8yRHC4ox83LSlMsQ==", "path": "system.security.permissions/4.7.0", "hashPath": "system.security.permissions.4.7.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "path": "system.security.principal.windows/4.7.0", "hashPath": "system.security.principal.windows.4.7.0.nupkg.sha512"}, "System.Security.SecureString/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PnXp38O9q/2Oe4iZHMH60kinScv6QiiL2XH54Pj2t0Y6c2zKPEiAZsM/M3wBOHLNTBDFP0zfy13WN2M0qFz5jg==", "path": "system.security.securestring/4.3.0", "hashPath": "system.security.securestring.4.3.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-aeu4FlaUTemuT1qOd1MyU4T516QR4Fy+9yDbwWMPHOHy7U8FD6SgTzdZFO7gHcfAPHtECqInbwklVvUK4RHcNg==", "path": "system.text.encoding.codepages/4.7.0", "hashPath": "system.text.encoding.codepages.4.7.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-Xg4G4Indi4dqP1iuAiMSwpiWS54ZghzR644OtsRCm/m/lBMG8dUBhLVN7hLm8NNrNTR+iGbshCPTwrvxZPlm4g==", "path": "system.text.encodings.web/4.5.0", "hashPath": "system.text.encodings.web.4.5.0.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RpT2DA+L660cBt1FssIE9CAGpLFdFPuheB7pLpKpn6ZXNby7jDERe8Ua/Ne2xGiwLVG2JOqziiaVCGDon5sKFA==", "path": "system.text.regularexpressions/4.3.0", "hashPath": "system.text.regularexpressions.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Threading.Timer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Z6YfyYTCg7lOZjJzBjONJTFKGN9/NIYKSxhU5GRd+DTwHSZyvWp1xuI5aR+dLg+ayyC5Xv57KiY4oJ0tMO89fQ==", "path": "system.threading.timer/4.3.0", "hashPath": "system.threading.timer.4.3.0.nupkg.sha512"}, "System.Windows.Extensions/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-CeWTdRNfRaSh0pm2gDTJFwVaXfTq6Xwv/sA887iwPTneW7oMtMlpvDIO+U60+3GWTB7Aom6oQwv5VZVUhQRdPQ==", "path": "system.windows.extensions/4.7.0", "hashPath": "system.windows.extensions.4.7.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "path": "system.xml.xdocument/4.3.0", "hashPath": "system.xml.xdocument.4.3.0.nupkg.sha512"}, "System.Xml.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lJ8AxvkX7GQxpC6GFCeBj8ThYVyQczx2+f/cWHJU8tjS7YfI6Cv6bon70jVEgs2CiFbmmM8b9j1oZVx0dSI2Ww==", "path": "system.xml.xmldocument/4.3.0", "hashPath": "system.xml.xmldocument.4.3.0.nupkg.sha512"}, "System.Xml.XmlSerializer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-MYoTCP7EZ98RrANESW05J5ZwskKDoN0AuZ06ZflnowE50LTpbR5yRg3tHckTVm5j/m47stuGgCrCHWePyHS70Q==", "path": "system.xml.xmlserializer/4.3.0", "hashPath": "system.xml.xmlserializer.4.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Antiforgery/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Cookies.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Core.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.OAuth/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization.Policy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Authorization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Forms/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Server/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Connections.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.CookiePolicy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cors/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics.HealthChecks/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HostFiltering/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting.Server.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Html.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Connections.Common/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Connections/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Features.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpOverrides/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpsPolicy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Identity.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Localization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Localization.Routing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Metadata/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.ApiExplorer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Cors/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Formatters.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Formatters.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Localization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Razor/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.RazorPages/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.TagHelpers/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.ViewFeatures/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Razor.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Razor.Runtime.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCaching/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCompression/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Rewrite/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Routing.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Routing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.HttpSys/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.IIS/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.IISIntegration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Session/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Common/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Protocols.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.StaticFiles/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.WebSockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.WebUtilities.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.CSharp.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Caching.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Caching.Memory.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Binder/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.CommandLine/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.EnvironmentVariables/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.FileExtensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Ini/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.KeyPerFile/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.UserSecrets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.DependencyInjection.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.HealthChecks/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Composite/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Physical/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileSystemGlobbing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Hosting.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Hosting/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Http/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Localization.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Localization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Configuration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Console/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Debug/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.EventLog/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.EventSource/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.TraceSource/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.ObjectPool.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.ConfigurationExtensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.WebEncoders.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.JSInterop/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Net.Http.Headers.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.VisualBasic.Core/10.0.6.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.VisualBasic/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Win32.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Win32.Registry.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "mscorlib/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "netstandard/2.1.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.AppContext.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Buffers.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Concurrent.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Immutable.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.NonGeneric.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Specialized.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Annotations.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.EventBasedAsync/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.TypeConverter.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Configuration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Console.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data.Common/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data.DataSetExtensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Contracts/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Debug.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.EventLog/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.FileVersionInfo/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Process/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.StackTrace/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.TextWriterTraceListener/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Tools.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.TraceSource/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Tracing.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Drawing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Drawing.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Dynamic.Runtime/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Formats.Asn1/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Calendars.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.Brotli/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.FileSystem/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.ZipFile.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.DriveInfo/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Watcher/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.IsolatedStorage/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.MemoryMappedFiles/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipelines/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipes/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.UnmanagedMemoryStream/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Expressions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Parallel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Queryable/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Memory.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Http.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Http.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.HttpListener/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Mail/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.NameResolution.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.NetworkInformation/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Ping/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Requests/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Security/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.ServicePoint/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Sockets.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebClient/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebHeaderCollection/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebProxy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebSockets.Client/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebSockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Numerics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Numerics.Vectors/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ObjectModel.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.DispatchProxy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.ILGeneration.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.Lightweight.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Metadata.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.TypeExtensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.Reader/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.ResourceManager.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.Writer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.CompilerServices.Unsafe.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.CompilerServices.VisualC/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Handles.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.RuntimeInformation.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Intrinsics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Loader/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Numerics.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Formatters.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Json.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.AccessControl.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Claims/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Algorithms.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Cng.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Csp.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Encoding.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.X509Certificates.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Xml.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Permissions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Principal/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Principal.Windows.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.SecureString.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceModel.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceProcess/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.CodePages.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encodings.Web.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.RegularExpressions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Channels/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Overlapped/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Dataflow/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Parallel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Thread/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.ThreadPool/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Timer.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Transactions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Transactions.Local/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ValueTuple/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Web.HttpUtility/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Windows/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Windows.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.Linq/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.ReaderWriter.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.Serialization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XDocument.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XmlDocument.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XmlSerializer.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XPath/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XPath.XDocument/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "WindowsBase/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}}}