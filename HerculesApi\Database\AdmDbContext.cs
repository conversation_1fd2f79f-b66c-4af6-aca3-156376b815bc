﻿using HerculesApi.Controllers;
using HerculesApi.Models;
using HerculesApi.Models;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace HerculesApi.Database
{
    public class AdmDbContext : IdentityDbContext<ApplicationUser>
    {
        public AdmDbContext(DbContextOptions<AdmDbContext> options) : base(options)
        {

        }
        public DbSet<Usuario> Usuarios { get; set; }
        public DbSet<RelacUserAplicacao> RelUsersAplicacoes { get; set; }
    }
}
