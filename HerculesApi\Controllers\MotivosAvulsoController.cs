﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using HerculesApi.Database;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using HerculesApi.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Microsoft.AspNetCore.Authorization;

namespace HerculesApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class MotivosAvulsoController : ControllerBase
    {
        private readonly HerculesDbContext _context;
        public MotivosAvulsoController(HerculesDbContext context)
        {
            _context = context;
        }
        [HttpGet]
        public async Task<ActionResult<IEnumerable<MotivoAvulso>>> GetMotivosAvulso()
        {
            var motivos = _context.MotivosAvulso.ToList();
            if (motivos.Count > 0)
            {
                return motivos;
            }
            else
            {
                return NotFound("Não há itens em motivos");
            }
        }
        [HttpGet("{id}")]
        public async Task<ActionResult<MotivoAvulso>> GetMotivoAvulso(int id)
        {
            var motivos = await _context.MotivosAvulso.FindAsync(id);
            if (motivos == null)
            {
                return NotFound("Motivo não encontrado");
            }

            return motivos;
        }
        [HttpPost]
        public async Task<ActionResult<MotivoAvulso>> PostMotivoAvulso(MotivoAvulso motivo)
        {
            
            var mot = _context.MotivosAvulso
                .Where(x => x.mav_descricao.ToLower().Equals(motivo.mav_descricao.ToLower()))
                .AsNoTracking()
                .FirstOrDefault();
            if (mot != null)
            {
                return BadRequest(new { message = "Não é possível cadastrar motivo já cadastrado!" });
            }
            else
            {
                motivo.mav_descricao = motivo.mav_descricao.ToLower();
                _context.MotivosAvulso.Add(motivo);
                await _context.SaveChangesAsync();

                return Ok("Motivo cadastrado com sucesso!");
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> PutMotivoAvulso(int id, MotivoAvulso motivo)
        {
            var mot = _context.MotivosAvulso.AsNoTracking().Where(x => x.id == id).FirstOrDefault();
            if (mot == null)
            {
                return BadRequest("Motivo não encontrado");
            }

            _context.Entry(motivo).State = EntityState.Modified;
            try
            {
                await _context.SaveChangesAsync();                
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!MotivosExists(id))
                {
                    return NotFound("Motivo não encontrado");
                }
                else
                {
                    throw;
                }
            }

            return Ok("Motivo alterado com sucesso!");
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteMotivoAvulso(int id)
        {
            var motivo = await _context.MotivosAvulso.FindAsync(id);
            if (motivo == null)
            {
                return NotFound("Motivo não encontrado");
            }
            else
            {
                _context.MotivosAvulso.Remove(motivo);
                await _context.SaveChangesAsync();

                return Ok("Motivo excluido com sucesso!");
            }

        }
        private bool MotivosExists(int id)
        {
            return _context.MotivosAvulso.Any(e => e.id == id);
        }
    }
}