﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HerculesApi.Models
{
    public class Ouvidoria
    {
        [Key]
        public int id { get; set; }
        public int ouv_na { get; set; }
        public DateTime? ouv_data { get; set; }
        public string? ouv_municipio { get; set; }
        public string? ouv_nucleo { get; set; }
        public string? ouv_observacao { get; set; }
        public string? ouv_gps { get; set; }
        [NotMapped]
        public int ouv_ordId { get; set; }
        [NotMapped]
        public int ouv_municipioId { get; set; }
        [NotMapped]
        public int nucleoId { get; set; }
        public int ouv_status { get; set; }
        [NotMapped]
        public List<ClienteOuvidoria> clientes { get; set; }
        [NotMapped]
        public List<int>? userId { get; set; }
        public Ouvidoria()
        {
            clientes = new List<ClienteOuvidoria>();
            userId = new List<int>();
        }
    }
}
