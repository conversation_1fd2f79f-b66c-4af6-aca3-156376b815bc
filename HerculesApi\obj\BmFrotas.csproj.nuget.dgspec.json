{"format": 1, "restore": {"C:\\Dev\\GitHub\\baramaiamv-api\\BmFrotas\\BmFrotas.csproj": {}}, "projects": {"C:\\Dev\\GitHub\\baramaiamv-api\\BmFrotas\\BmFrotas.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\GitHub\\baramaiamv-api\\BmFrotas\\BmFrotas.csproj", "projectName": "BmFrotas", "projectPath": "C:\\Dev\\GitHub\\baramaiamv-api\\BmFrotas\\BmFrotas.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\GitHub\\baramaiamv-api\\BmFrotas\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net5.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "dependencies": {"Microsoft.AspNetCore.JsonPatch": {"target": "Package", "version": "[5.0.16, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[5.0.17, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[5.0.17, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[5.0.17, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[5.0.17, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[5.0.17, )"}, "Microsoft.VisualStudio.Web.CodeGeneration.Design": {"target": "Package", "version": "[5.0.2, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[5.6.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.407\\RuntimeIdentifierGraph.json"}}}}}