﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using HerculesApi.Database;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using HerculesApi.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Microsoft.AspNetCore.Authorization;

namespace HerculesApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class FotosController : ControllerBase
    {
        private readonly HerculesDbContext _context;
        public FotosController(HerculesDbContext context)
        {
            _context = context;
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<Foto>> GetFoto(int id)
        {
            var foto = await _context.Fotos.FindAsync(id);
            if (foto == null)
            {
                return NotFound("Foto não encontrada");
            }

            return foto;
        }
        [HttpPut("{id}")]
        public async Task<IActionResult> PutFoto(int id, Foto foto)
        {
            var fot = _context.Fotos.Where(x => x.id == id).FirstOrDefault();
            if (fot == null)
            {
                return BadRequest("Foto não encontrada");
            }
            if (!string.IsNullOrEmpty(foto.foto_dado_base64))
            {
                foto.foto_dado = Convert.FromBase64String(foto.foto_dado_base64);
            }

            _context.Entry(foto).State = EntityState.Modified;
            try
            {
                await _context.SaveChangesAsync();                
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!EquipamentoExists(id))
                {
                    return NotFound("Equipamento não encontrado");
                }
                else
                {
                    throw;
                }
            }

            return Ok("Equipamento alterado com sucesso!");
        }
        private bool EquipamentoExists(int id)
        {
            return _context.EquipamentosVisitas.Any(e => e.id == id);
        }
    }
}