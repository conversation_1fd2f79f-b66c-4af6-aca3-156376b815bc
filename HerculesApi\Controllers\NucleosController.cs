﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using HerculesApi.Database;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using HerculesApi.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Runtime.ConstrainedExecution;
using System.Net.WebSockets;
using Microsoft.AspNetCore.Authorization;
using System.IO;
using Microsoft.IdentityModel.Tokens;
using DocumentFormat.OpenXml.Office.CustomUI;

namespace HerculesApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class NucleosController : ControllerBase
    {
        private readonly HerculesDbContext _context;
        private readonly AdmDbContext _contextAdm;
        public NucleosController(HerculesDbContext context, AdmDbContext contextAdm)
        {
            _context = context;
            _contextAdm = contextAdm;
        }
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Nucleo>>> GetNucleos()
        {
            var nucleos = _context.Nucleos.AsNoTracking().OrderBy(x => x.nuc_descricao).ToList();
            if (nucleos.Count > 0)
            {
                var rel = _context.RelMunicipiosNucleos.AsNoTracking().ToList();
                var relEmp = _context.RelNucleosEmpreiteiras.AsNoTracking().ToList();
                foreach (var nuc in nucleos)
                {
                    if (rel != null)
                    {
                        foreach (var item in rel)
                        {
                            if (nuc.id == item.nucId)
                            {
                                nuc.munId = item.munId;
                            }
                        }
                    }

                    if (relEmp != null)
                    {
                        foreach (var r in relEmp)
                        {
                            if (nuc.id == r.nucId)
                            {
                                nuc.empreiteiraId = r.empId;
                            }
                        }
                    }
                }
                return nucleos;
            }
            else
            {
                return NotFound("Não há núcleos");
            }
        }
        [HttpGet("{id}")]
        public async Task<ActionResult<Nucleo>> GetNucleo(int id)
        {
            var nucleo = await _context.Nucleos.FindAsync(id);
            if (nucleo == null)
            {
                return NotFound("Núcleo não encontrado");
            }
            var rel = _context.RelMunicipiosNucleos.Where(x => x.nucId == id).FirstOrDefault();
            if (rel != null)
            {
                nucleo.munId = rel.munId;
            }
            var relEmp = _context.RelNucleosEmpreiteiras.Where(y => y.nucId == id).FirstOrDefault();

            if (relEmp != null)
            {
                nucleo.empreiteiraId = relEmp.empId;
            }
            return nucleo;
        }
        [HttpGet("municipio/{municipioid}")]
        public async Task<ActionResult<IEnumerable<Nucleo>>> GetNucleoMunicipios(int municipioid)
        {
            var municipio = _context.Municipios.Where(x => x.id == municipioid).FirstOrDefault();
            if (municipio == null)
            {
                return NotFound("Município não existente!");
            }
            var relac = _context.RelMunicipiosNucleos.AsNoTracking().Where(x => x.munId == municipioid).Select(a => a.nucId).ToList();
            var nucleos = _context.Nucleos.Where(x => relac.Contains(x.id)).ToList();
            if (nucleos.Count > 0)
            {
                foreach (var nucleo in nucleos)
                {
                    nucleo.munId = municipioid;
                    var relEmp = _context.RelNucleosEmpreiteiras.Where(y => y.nucId == nucleo.id).FirstOrDefault();
                    if (relEmp != null)
                    {
                        nucleo.empreiteiraId = relEmp.empId;
                    }
                }
                return nucleos;
            }
            else
            {
                return NotFound("Não há núcleos");
            }
        }
        [HttpGet("gerais/municipio/{municipioid}")]
        public async Task<ActionResult<IEnumerable<ViewNucleo>>> GetNucleoGerais(int municipioid)
        {
            var municipio = _context.Municipios.Where(x => x.id == municipioid).FirstOrDefault();
            if (municipio == null)
            {
                return NotFound("Município não existente!");
            }
            var nucleos = _context.vw_nucleos.Where(x => x.municipioId == municipioid).ToList();
            if (nucleos.Count > 0)
            {
                foreach (var item in nucleos)
                {
                    item.totalServicos = item.qtdCorrecao + item.qtdRetirada + item.qtdProvisorio;
                }
                return nucleos;
            }
            else
            {
                return NotFound("Não há núcleos");
            }
        }
        [HttpPost]
        public async Task<ActionResult<Nucleo>> PostNucleo(Nucleo nucleo)
        {
            nucleo.nuc_descricao.ToUpper();
            var nuc = _context.Nucleos
                .Where(x => x.nuc_descricao.ToLower().Equals(nucleo.nuc_descricao.ToLower()))
                .AsNoTracking()
                .FirstOrDefault();

            if (nuc != null)
            {
                return BadRequest(new { message = "Não é possível cadastrar núcleo já cadastrado!" });
            }

            _context.Nucleos.Add(nucleo);
            await _context.SaveChangesAsync();

            RelMunicipioNucleo rel = new RelMunicipioNucleo
            {
                nucId = nucleo.id,
                munId = Convert.ToInt32(nucleo.munId)
            };

            _context.RelMunicipiosNucleos.Add(rel);

            RelNucleoEmpreiteira relEmp = new RelNucleoEmpreiteira
            {
                nucId = nucleo.id,
                empId = Convert.ToInt32(nucleo.empreiteiraId)
            };
            _context.RelNucleosEmpreiteiras.Add(relEmp);

            await _context.SaveChangesAsync();

            return Ok("Núcleo cadastrado com sucesso!");
        }
        [HttpPost("importar-nucleos")]
        public async Task<IActionResult> ImportarNucleos(IFormFile file)
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest("Arquivo inválido.");
            }

            try
            {
                using var stream = new MemoryStream();
                await file.CopyToAsync(stream);

                using var workbook = new ClosedXML.Excel.XLWorkbook(stream);
                var worksheet = workbook.Worksheet(1); // Obtém a primeira aba do Excel

                var rowCount = worksheet.LastRowUsed().RowNumber();

                // Processa as linhas do Excel
                for (int row = 2; row <= rowCount; row++) // Começa na linha 2 para ignorar o cabeçalho
                {
                    var nucMunicipio = worksheet.Cell(row, 1).GetString().Trim();
                    var nucDescricao = worksheet.Cell(row, 2).GetString().Trim();
                    var nucEmpreiteira = worksheet.Cell(row, 3).GetString().Trim();
                    var nucGps = worksheet.Cell(row, 4).GetString().Trim();

                    if (!string.IsNullOrWhiteSpace(nucDescricao))
                    {
                        RelNucleoEmpreiteira relNucEmp = new RelNucleoEmpreiteira();
                        RelMunicipioNucleo relMunNuc = new RelMunicipioNucleo();
                        Municipio m = new Municipio();
                        Empreiteira e = new Empreiteira();

                        if (!nucEmpreiteira.IsNullOrEmpty())
                        {
                            var empreiteira = _context.Empreiteiras.AsNoTracking().Where(x => x.emp_desc.Equals(nucEmpreiteira)).FirstOrDefault();
                            if (empreiteira == null)
                            {
                                var e1 = new Empreiteira
                                {
                                    emp_desc = nucEmpreiteira
                                };
                                _context.Empreiteiras.Add(e1);
                                await _context.SaveChangesAsync();
                                e = e1;
                            }
                            if (empreiteira != null)
                            {
                                e = empreiteira;
                            }
                        }

                        var mun = _context.Municipios.AsNoTracking().Where(x => x.mun_descricao.Equals(nucMunicipio)).FirstOrDefault();
                        if (mun == null)
                        {
                            var m1 = new Municipio()
                            {
                                mun_descricao = nucMunicipio
                            };
                            _context.Municipios.Add(m1);
                            await _context.SaveChangesAsync();
                            m = m1;
                        }
                        if (mun != null)
                        {
                            m = mun;
                        }

                        // Verifica se já existe no banco
                        var nucleoExistente = _context.Nucleos
                            .FirstOrDefault(n => n.nuc_descricao.Equals(nucDescricao));

                        if (nucleoExistente == null)
                        {
                            // Adiciona novo núcleo
                            var novoNucleo = new Nucleo
                            {
                                nuc_descricao = nucDescricao,
                                nuc_gps = nucGps
                            };
                            _context.Nucleos.Add(novoNucleo);
                            await _context.SaveChangesAsync();

                            relMunNuc.nucId = novoNucleo.id;
                            relMunNuc.munId = m.id;
                            _context.RelMunicipiosNucleos.Add(relMunNuc);
                            await _context.SaveChangesAsync();

                            if (!nucEmpreiteira.IsNullOrEmpty())
                            {
                                relNucEmp.nucId = novoNucleo.id;
                                relNucEmp.empId = e.id;
                                _context.RelNucleosEmpreiteiras.Add(relNucEmp);
                                await _context.SaveChangesAsync();
                            }
                        }
                        else
                        {
                            // Atualiza o núcleo existente
                            if (!string.IsNullOrWhiteSpace(nucGps))
                            {
                                nucleoExistente.nuc_gps = nucGps;
                                _context.Nucleos.Update(nucleoExistente);
                                await _context.SaveChangesAsync();
                            }

                            // Verifica e atualiza relações
                            var rel = _context.RelNucleosEmpreiteiras
                                .FirstOrDefault(x => x.nucId == nucleoExistente.id);

                            if (rel == null && !nucEmpreiteira.IsNullOrEmpty())
                            {
                                relNucEmp.nucId = nucleoExistente.id;
                                relNucEmp.empId = e.id;
                                _context.RelNucleosEmpreiteiras.Add(relNucEmp);
                                await _context.SaveChangesAsync();
                            }
                        }

                    }
                }

                await _context.SaveChangesAsync();

                return Ok("Importação realizada com sucesso!");
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro ao processar o arquivo: {ex.Message}");
            }
        }


        [HttpPut("{id}")]
        public async Task<IActionResult> PutNucleo(int id, Nucleo nucleo)
        {
            nucleo.nuc_descricao.ToUpper();
            var mun = _context.Municipios.AsNoTracking().Where(x => x.id == nucleo.munId).FirstOrDefault();
            var nuc = _context.Nucleos.Where(x => x.id == id).AsNoTracking().FirstOrDefault();
            var emp = _context.Empreiteiras.AsNoTracking().Where(y => y.id == nucleo.empreiteiraId).FirstOrDefault();
            if (nuc == null)
            {
                return BadRequest("Núcleo não encontrado");
            }

            _context.Entry(nucleo).State = EntityState.Modified;
            try
            {
                if (mun != null)
                {
                    await _context.SaveChangesAsync();
                    // Verifica se já existe um relacionamento entre o núcleo e o município
                    var rel = _context.RelMunicipiosNucleos
                        .Where(r => r.nucId == nucleo.id)
                        .FirstOrDefault();

                    // Se não existir relacionamento, cria e insere
                    if (rel == null)
                    {
                        RelMunicipioNucleo novoRel = new RelMunicipioNucleo
                        {
                            munId = mun.id,
                            nucId = nucleo.id
                        };

                        _context.RelMunicipiosNucleos.Add(novoRel);
                        await _context.SaveChangesAsync();
                    }
                    else
                    {
                        _context.RelMunicipiosNucleos.Remove(rel);
                        await _context.SaveChangesAsync();

                        RelMunicipioNucleo novoRel = new RelMunicipioNucleo
                        {
                            munId = mun.id,
                            nucId = nucleo.id
                        };

                        _context.RelMunicipiosNucleos.Add(novoRel);
                        await _context.SaveChangesAsync();
                    }
                    if (emp != null)
                    {
                        var relEmp = _context.RelNucleosEmpreiteiras
                        .Where(r => r.nucId == nucleo.id)
                        .FirstOrDefault();

                        // Se não existir relacionamento, cria e insere
                        if (relEmp == null)
                        {
                            RelNucleoEmpreiteira novoRelEmp = new RelNucleoEmpreiteira
                            {
                                empId = emp.id,
                                nucId = nucleo.id
                            };

                            _context.RelNucleosEmpreiteiras.Add(novoRelEmp);
                            await _context.SaveChangesAsync();
                        }
                        else
                        {
                            _context.RelNucleosEmpreiteiras.Remove(relEmp);
                            await _context.SaveChangesAsync();

                            RelNucleoEmpreiteira novoRelEmp = new RelNucleoEmpreiteira
                            {
                                empId = emp.id,
                                nucId = nucleo.id
                            };

                            _context.RelNucleosEmpreiteiras.Add(novoRelEmp);
                            await _context.SaveChangesAsync();
                        }
                    }
                    else
                    {
                        return NotFound("Empreiteira não encontrada para relacionamento");
                    }
                    return Ok("Núcleo alterado com sucesso!");
                }
                else
                {
                    return NotFound("Município não encontrado para relacionamento");
                }

            }
            catch (DbUpdateConcurrencyException)
            {
                if (!NucleoExists(id))
                {
                    return NotFound("Núcleo não encontrado");
                }
                else
                {
                    throw;
                }
            }

        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteNucleo(int id)
        {
            var nucleo = await _context.Nucleos.FindAsync(id);
            if (nucleo == null)
            {
                return NotFound("Núcleo não encontrado");
            }
            else
            {
                _context.Nucleos.Remove(nucleo);
                await _context.SaveChangesAsync();

                var rel = _context.RelMunicipiosNucleos
                        .AsNoTracking()
                        .Where(r => r.nucId == nucleo.id)
                        .FirstOrDefault();

                _context.RelMunicipiosNucleos.Remove(rel);
                await _context.SaveChangesAsync();

                return Ok("Núcleo excluido com sucesso!");
            }

        }
        private bool NucleoExists(int id)
        {
            return _context.Nucleos.Any(e => e.id == id);
        }
    }
}