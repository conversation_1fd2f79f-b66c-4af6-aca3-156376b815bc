﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using HerculesApi.Database;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using HerculesApi.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using System.Runtime.ConstrainedExecution;
using Microsoft.AspNetCore.Authorization;
using DocumentFormat.OpenXml.Wordprocessing;
using DocumentFormat.OpenXml.Office2010.Excel;

namespace HerculesApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class OrdensServicoController : ControllerBase
    {
        private readonly HerculesDbContext _context;
        private readonly AdmDbContext _contextAdm;
        public OrdensServicoController(HerculesDbContext context, AdmDbContext contextAdm)
        {
            _context = context;
            _contextAdm = contextAdm;
        }
        [HttpGet]
        public async Task<ActionResult<IEnumerable<ViewOrdemServico>>> GetOrdens()
        {
            var ordens = _context.vw_ordens_servico.AsNoTracking().ToList();

            if (ordens.Count > 0)
            {
                return Ok(ordens);
            }
            else
            {
                return NotFound("Não há ordens de serviço!");
            }
        }
        [HttpGet("{pageNumber}/{pageSize}")]
        public async Task<ActionResult<IEnumerable<ViewOrdemServico>>> GetOrdens(int pageNumber, int pageSize)
        {
            int countOrdens = _context.OrdensServico.AsNoTracking().Count();
            var skip = (pageNumber - 1) * pageSize;
            var ordens = await _context.vw_ordens_servico
        .AsNoTracking()
        .Skip(skip)
        .Take(pageSize)
        .ToListAsync();

            if (ordens.Count > 0)
            {
                foreach (var item in ordens)
                {
                    if (!item.ord_motivo.IsNullOrEmpty())
                    {
                        item.ord_tipoServ = "CORREÇÃO";
                        item.motivo = item.ord_motivo;
                    }
                    if (item.ord_nome == "CLIENTE PROVISÓRIO" && item.ord_motivo.IsNullOrEmpty())
                    {
                        item.ord_tipoServ = "CLIENTE PROVISÓRIO";
                        item.motivo = item.ord_tipoServ;
                    }
                    if (item.ord_status_uc == "AGUARDA RETIRADA DE MEDIDOR" && item.ord_motivo.IsNullOrEmpty())
                    {
                        item.ord_tipoServ = "RETIRADA DE MEDIDOR";
                        item.motivo = item.ord_tipoServ;
                    }
                    if (item.ord_na > 0)
                    {
                        item.ord_tipoServ = "OUVIDORIA";
                        item.motivo = item.ord_tipoServ;
                    }
                }
                return Ok(ordens);
            }
            else
            {
                return NotFound("Não há ordens de serviço!");
            }
        }
        [HttpGet("nucleo/ordemid/{ordemid}")]
        public async Task<ActionResult<IEnumerable<OrdemServico>>> GetOrdensNucleo(int ordemid)
        {
            var ordem = _context.OrdensServico.AsNoTracking().Where(x => x.id == ordemid).FirstOrDefault();
            var ordens = _context.OrdensServico.AsNoTracking().Where(y => y.ord_nucleo == ordem.ord_nucleo && y.id != ordemid).ToList();

            if (ordens.Count > 0)
            {
                foreach (var item in ordens)
                {
                    if (!item.ord_motivo.IsNullOrEmpty())
                    {
                        item.ord_tipoServ = "CORREÇÃO";
                        item.motivo = item.ord_motivo;
                    }
                    if (item.ord_nome == "CLIENTE PROVISÓRIO" && item.ord_motivo.IsNullOrEmpty())
                    {
                        item.ord_tipoServ = "CLIENTE PROVISÓRIO";
                        item.motivo = item.ord_tipoServ;
                    }
                    if (item.ord_status_uc == "AGUARDA RETIRADA DE MEDIDOR" && item.ord_motivo.IsNullOrEmpty())
                    {
                        item.ord_tipoServ = "RETIRADA DE MEDIDOR";
                        item.motivo = item.ord_tipoServ;
                    }
                    if (item.ord_na > 0)
                    {
                        item.ord_tipoServ = "OUVIDORIA";
                        item.motivo = item.ord_tipoServ;
                    }

                    // Garantir que as listas não são nulas
                    if (item.userId == null)
                        item.userId = new List<int>();

                    var users = await _context.RelOrdensTecnicos
                         .AsNoTracking()
                         .Where(x => x.ordemId == item.id)
                         .Select(x => x.tecId)
                         .ToListAsync();
                    item.userId.AddRange(users);

                    if (users.Count > 0)
                    {
                        string nome = "";
                        List<string> lista = new List<string>();
                        foreach (var usu in users)
                        {
                            var usuario = _contextAdm.Users.AsNoTracking().Where(y => y.UserId == usu).FirstOrDefault();
                            string usunome = usuario.FirstName + " " + usuario.LastName;
                            lista.Add(usunome);
                        }
                        foreach (var n in lista)
                        {
                            nome = nome + n + " - ";
                        }
                        item.tecnicos = nome;
                        item.tecnicos = item.tecnicos.Substring(0, item.tecnicos.Length - 3);
                    }
                }
                return Ok(ordens);
            }
            else
            {
                return NotFound("Não há outras ordens de serviço para este núcleo!");
            }
        }
        [HttpGet("et/{et}/{ordemid}")]
        public async Task<ActionResult> GetOrdemEt(int et, int ordemid)
        {
            var ordens = _context.vw_ordens_servico
                .AsNoTracking()
                .Where(x => (x.ord_et == et) && (x.ord_status == 0) && (x.id != ordemid))
                .ToList();

            if (!ordens.Any())
            {
                return NotFound("Nenhuma ordem de serviço encontrada!");
            }
            return Ok(ordens);
        }
        [HttpGet("userid/{userid}")]
        public async Task<ActionResult> GetOrdemUsuario(int userid)
        {
            var relaOrdemUser = _context.RelOrdensTecnicos
                .AsNoTracking()
                .Where(x => x.tecId == userid)
                .Select(x => x.ordemId)
                .ToList();

            if (relaOrdemUser == null || !relaOrdemUser.Any())
            {
                return NotFound("Não há ordem de serviço vinculada ao Usuário!");
            }

            var ordens = _context.vw_ordens_servico
                .AsNoTracking()
                .Where(x => (relaOrdemUser.Contains(x.id)) && (x.ord_status == 0))
                .ToList();

            if (!ordens.Any())
            {
                return NotFound("Nenhuma ordem de serviço encontrada!");
            }

            foreach (var item in ordens)
            {
                if (!string.IsNullOrEmpty(item.ord_motivo))
                {
                    item.ord_tipoServ = "CORREÇÃO";
                    item.motivo = item.ord_motivo;
                }
                else if (item.ord_nome == "CLIENTE PROVISÓRIO")
                {
                    item.ord_tipoServ = "CLIENTE PROVISÓRIO";
                    item.motivo = item.ord_tipoServ;
                }
                else if (item.ord_status_uc == "AGUARDA RETIRADA DE MEDIDOR")
                {
                    item.ord_tipoServ = "RETIRADA DE MEDIDOR";
                    item.motivo = item.ord_tipoServ;
                }
                else if (item.ord_na > 0)
                {
                    item.ord_tipoServ = "OUVIDORIA";
                    item.motivo = item.ord_tipoServ;
                }
            }

            // Agrupando os resultados por ord_et
            var groupedOrdens = ordens
                .GroupBy(o => o.ord_et)
                .Select(g => new
                {
                    OrdET = g.Key, // Chave de agrupamento
                    Ordens = g.ToList() // Lista de ordens no grupo
                })
                .ToList();

            return Ok(groupedOrdens);
        }
        [HttpGet("mobile/userid/{userid}")]
        public async Task<ActionResult<IEnumerable<OrdemEt>>> GetOrdemUsuarioEt(int userid)
        {
            List<OrdemEt> ordensEt = new List<OrdemEt>();
            var relaOrdemUser = _context.RelOrdensTecnicos
                .AsNoTracking()
                .Where(x => x.tecId == userid)
                .Select(x => x.ordemId)
                .ToList();

            if (relaOrdemUser == null || !relaOrdemUser.Any())
            {
                return NotFound("Não há ordem de serviço vinculada ao Usuário!");
            }

            var ordens = _context.vw_ordens_servico
                .AsNoTracking()
                .OrderBy(c => c.ord_cp)
                .Where(x => (relaOrdemUser.Contains(x.id)) && (x.ord_status == 0) && (x.ord_na == 0 || x.ord_na ==null))
                .ToList();

            if (!ordens.Any())
            {
                return NotFound("Nenhuma ordem de serviço encontrada!");
            }

            foreach (var item in ordens)
            {
                if (!string.IsNullOrEmpty(item.ord_motivo))
                {
                    item.ord_tipoServ = "CORREÇÃO";
                    item.motivo = item.ord_motivo;
                }
                else if (item.ord_nome == "CLIENTE PROVISÓRIO")
                {
                    item.ord_tipoServ = "CLIENTE PROVISÓRIO";
                    item.motivo = item.ord_tipoServ;
                }
                else if (item.ord_status_uc == "AGUARDA RETIRADA DE MEDIDOR")
                {
                    item.ord_tipoServ = "RETIRADA DE MEDIDOR";
                    item.motivo = item.ord_tipoServ;
                }
                else if (item.ord_na > 0)
                {
                    item.ord_tipoServ = "OUVIDORIA";
                    item.motivo = item.ord_tipoServ;
                }
            }

            // Agrupando os resultados por ord_et
            var groupedOrdens = ordens
                .GroupBy(o => o.ord_et)
                .Select(g => new
                {
                    OrdET = g.Key, // Chave de agrupamento
                    Ordens = g.ToList() // Lista de ordens no grupo
                })
                .ToList();
            foreach (var item in groupedOrdens)
            {
                OrdemEt o1 = new OrdemEt();
                o1.et = Convert.ToInt32(item.OrdET);
                o1.cp = Convert.ToInt32(item.Ordens[0].ord_cp);
                o1.nucleo = item.Ordens[0].ord_nucleo;
                o1.empreiteira = item.Ordens[0].ord_empreiteira;
                o1.ordens = item.Ordens;

                ordensEt.Add(o1);
            }
            return Ok(ordensEt);
        }
        [HttpPost]
        public async Task<IActionResult> PostOrdem([FromBody] OrdemDTO selecoes)
        {
            if (selecoes.Clientes == null || !selecoes.Clientes.Any())
            {
                return BadRequest("É necessário vincular ao menos um Cliente");
            }
            if (selecoes.Usuarios == null || !selecoes.Usuarios.Any())
            {
                return BadRequest("É necessário vincular ao menos um Usuário");
            }
            List<RelOrdemTecnico> listaRel = new List<RelOrdemTecnico>();
            List<OrdemServico> listaOrdem = new List<OrdemServico>();
            string nomes = "";
            foreach (var item in selecoes.Usuarios)
            {
                nomes = nomes + " - " + item.Name;
            }

            foreach (var cliente in selecoes.Clientes)
            {
                OrdemServico or = new OrdemServico();
                or.ord_na = 0;
                or.ord_ordem = "";
                or.ord_cliId = cliente.grl_id;
                or.ord_uc = cliente.grl_uc;                
                or.ord_nome = cliente.grl_nome;
                or.ord_municipio = cliente.grl_municipio;
                or.ord_nucleo = cliente.grl_nucleo;
                or.ord_logradouro = cliente.grl_logradouro;
                or.ord_numero = cliente.grl_numero;
                or.ord_complemento = cliente.grl_complemento;
                or.ord_bloco = cliente.grl_bloco;
                or.ord_cp = cliente.grl_cp;
                or.ord_cs = cliente.grl_cs;
                or.ord_pos1 = cliente.grl_pos1;
                or.ord_pos2 = cliente.grl_pos2;
                or.ord_pos3 = cliente.grl_pos3;
                or.ord_et = cliente.grl_et;
                or.ord_modulo = cliente.grl_modulo;
                or.ord_display = cliente.grl_display;
                or.ord_motivo = cliente.grl_motivo;
                or.ord_empreiteira = cliente.grl_empreiteira;
                or.ord_status_uc = cliente.grl_status_uc;
                or.ord_situacao = cliente.grl_situacao;
                or.ord_gps = cliente.grl_gps;
                or.ord_dataEntrada = DateTime.Now;
                or.ord_dataFechamento = null;
                or.ord_status = 0;
                or.ord_obs = cliente.grl_obs;
                or.ord_tecnicos = nomes;

                listaOrdem.Add(or);

            }
            _context.OrdensServico.AddRange(listaOrdem);
            await _context.SaveChangesAsync();

            foreach (var item in listaOrdem)
            {
                foreach (var usu in selecoes.Usuarios)
                {
                    RelOrdemTecnico rel = new RelOrdemTecnico();
                    rel.ordemId = item.id;
                    rel.tecId = usu.userId;

                    listaRel.Add(rel);
                }
            }
            _context.RelOrdensTecnicos.AddRange(listaRel);
            await _context.SaveChangesAsync();

            foreach (var e in selecoes.Clientes)
            {
                e.grl_vinculada = 1;
                _context.Gerais.Update(e);
                await _context.SaveChangesAsync();
            }

            return Ok("Ordens de serviços geradas com sucesso!");
        }


        [HttpPut("usuarios")]
        public async Task<IActionResult> UpdateOrdensTecnicos([FromBody] UsuarioDTO dto)
        {
            if (dto?.ordensId == null || !dto.ordensId.Any())
            {
                return BadRequest("Lista de ordens inválida.");
            }

            if (dto.usuarios == null)
            {
                return BadRequest("Lista de usuários inválida.");
            }

            List<int> ordensIds = dto.ordensId;
            List<int> usuariosIds = dto.usuarios.Select(x=>x.userId).ToList();
           
            

            // Recuperar ordens de serviço e os relacionamentos existentes
            var ordens = await _context.OrdensServico.Where(x => ordensIds.Contains(x.id)).ToListAsync();
            var relacoesExistentes = await _context.RelOrdensTecnicos.Where(x => ordensIds.Contains(x.ordemId)).ToListAsync();

            // Se a lista de usuários está vazia, remover relacionamentos e ordens
            if (!usuariosIds.Any())
            {
                _context.RelOrdensTecnicos.RemoveRange(relacoesExistentes);
                await _context.SaveChangesAsync();

                _context.OrdensServico.RemoveRange(ordens);
                await _context.SaveChangesAsync();

                return Ok("Todas as ordens de serviço foram removidas.");
            }

            // Obter IDs dos técnicos já associados
            var tecIdsExistentes = relacoesExistentes.Select(r => r.tecId).Distinct().ToList();

            // Verificar se já está atualizado
            if (tecIdsExistentes.OrderBy(x => x).SequenceEqual(usuariosIds.OrderBy(x => x)))
            {
                return Ok("Nenhuma alteração foi feita, a lista de técnicos já estava atualizada.");
            }

            // Determinar o que remover e adicionar
            var usuariosParaRemover = relacoesExistentes.Where(r => !usuariosIds.Contains(r.tecId)).ToList();
            var usuariosParaAdicionar = usuariosIds.Except(tecIdsExistentes).ToList();

            if (usuariosParaRemover.Any())
            {
                _context.RelOrdensTecnicos.RemoveRange(usuariosParaRemover);
            }
            string nomes = "";
            foreach (var item in dto.usuarios)
            {
                nomes = nomes + " - " + item.Name;
            }
            foreach (var ordem in ordens)
            {
                
                foreach (var usuarioId in usuariosParaAdicionar)
                {
                    var novaRelacao = new RelOrdemTecnico
                    {
                        ordemId = ordem.id,
                        tecId = usuarioId,
                    };
                    _context.RelOrdensTecnicos.Add(novaRelacao);
                    
                }
                ordem.ord_tecnicos = nomes;                
            }

            _context.OrdensServico.UpdateRange(ordens);

            await _context.SaveChangesAsync();

            return Ok("Ordens de serviço e relacionamentos atualizados com sucesso.");
        }

        [HttpPut("status")]
        public async Task<IActionResult> PutOrdemStatus(List<int> ids, int status)
        {

            var ords = _context.OrdensServico.Where(x => ids.Contains(x.id)).ToList();
            if (ords == null)
            {
                return BadRequest("Ordens de serviço não encontrada");
            }
            try
            {
                foreach (var ord in ords)
                {
                    ord.ord_status = status;
                    ord.ord_dataFechamento = DateTime.Now;

                    _context.OrdensServico.Update(ord);
                    await _context.SaveChangesAsync();

                    if (ord.ord_status == 0)
                    {
                        Visita visita = new Visita();
                        visita.vis_data = Convert.ToDateTime(ord.ord_dataFechamento);
                        visita.vis_ordId = ord.id;
                        visita.vis_efetivado = 1;

                        //_context.Visitas.Add(visita);
                        //await _context.SaveChangesAsync();
                    }
                    else
                    {
                        var visita = _context.Visitas.AsNoTracking().Where(x => x.vis_ordId == ord.id).FirstOrDefault();
                        if (visita != null)
                        {
                            //_context.Visitas.Remove(visita);
                            //await _context.SaveChangesAsync();
                        }
                    }
                }

            }
            catch (DbUpdateConcurrencyException)
            {
                return NotFound("Ordens de serviço não podem ser editadas");
            }

            return Ok("Ordens de serviço alteradas com sucesso!");
        }
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteOrdem(int id)
        {
            var ordem = _context.OrdensServico.AsNoTracking().FirstOrDefault(x => x.id == id);
            if (ordem == null)
            {
                return NotFound("Ordem de serviço não encontrada");
            }
            var visita = _context.Visitas.AsNoTracking().Where(x => x.vis_ordId == ordem.id).FirstOrDefault();
            if (visita == null)
            {
                if (ordem.ord_na != 0)
                {
                    var ouvidoria = _context.Ouvidorias.AsNoTracking().FirstOrDefault(x => x.ouv_na == ordem.ord_na);
                    if (ouvidoria == null)
                    {
                        return NotFound("Ouvidoria relacionada não encontrada");
                    }

                    var ordens = _context.OrdensServico.AsNoTracking().Where(x => x.ord_na == ouvidoria.ouv_na).ToList();
                    var rel = _context.RelOrdensTecnicos.AsNoTracking().Where(x => x.ordemId == ordem.id).ToList();



                    if (ordens.Count > 1)
                    {
                        _context.OrdensServico.Remove(ordem);
                        await _context.SaveChangesAsync();

                        if (rel.Count > 0)
                        {
                            _context.RelOrdensTecnicos.RemoveRange(rel);
                            await _context.SaveChangesAsync();
                        }
                    }
                    else if (ordens.Count == 1)
                    {
                        _context.OrdensServico.Remove(ordem);
                        await _context.SaveChangesAsync();

                        _context.Ouvidorias.Remove(ouvidoria);
                        await _context.SaveChangesAsync();

                        if (rel.Count > 0)
                        {
                            _context.RelOrdensTecnicos.RemoveRange(rel);
                            await _context.SaveChangesAsync();
                        }
                    }
                    return Ok("Ordem de serviço excluída com sucesso!");


                }
                else
                {
                    _context.OrdensServico.Remove(ordem);
                    await _context.SaveChangesAsync();

                    var relacionamentos = _context.RelOrdensTecnicos.AsNoTracking().Where(x => x.ordemId == id).ToList();
                    if (relacionamentos.Count > 0)
                    {
                        _context.RelOrdensTecnicos.RemoveRange(relacionamentos);
                        await _context.SaveChangesAsync();
                    }

                    var geral = _context.Gerais.AsNoTracking().FirstOrDefault(x => x.grl_id == ordem.ord_cliId);
                    if (geral != null)
                    {
                        geral.grl_vinculada = 0;
                        _context.Gerais.Update(geral);
                        await _context.SaveChangesAsync();
                    }


                    return Ok("Ordem de serviço excluída com sucesso!");
                }
            }
            else
            {
                return BadRequest("Existe visita relacionada a esta ordem");
            }

        }

    }
}