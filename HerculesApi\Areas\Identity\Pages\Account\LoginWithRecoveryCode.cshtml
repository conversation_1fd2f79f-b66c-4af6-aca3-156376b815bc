﻿@page
@model LoginWithRecoveryCodeModel
@{
    ViewData["Title"] = "Recovery code verification";
}

<h1>@ViewData["Title"]</h1>
<hr />
<p>
    You have requested to log in with a recovery code. This login will not be remembered until you provide
    an authenticator app code at log in or disable 2FA and log in again.
</p>
<div class="row">
    <div class="col-md-4">
        <form method="post">
            <div asp-validation-summary="All" class="text-danger"></div>
            <div class="form-group">
                <label asp-for="Input.RecoveryCode"></label>
                <input asp-for="Input.RecoveryCode" class="form-control" autocomplete="off" />
                <span asp-validation-for="Input.RecoveryCode" class="text-danger"></span>
            </div>
            <button type="submit" class="btn btn-primary">Log in</button>
        </form>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
