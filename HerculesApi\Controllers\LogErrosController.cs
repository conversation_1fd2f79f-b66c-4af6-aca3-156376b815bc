﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using HerculesApi.Database;
using HerculesApi.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNet.Identity;
using Microsoft.AspNetCore.Diagnostics;

namespace HerculesApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    [NotImplExceptionFilter]
    public class LogErrosController : ControllerBase
    {
        private readonly HerculesDbContext _context;

        public LogErrosController(HerculesDbContext context)
        {
            _context = context;
        }

        // GET: api/LogErros
        [HttpGet]
        public async Task<ActionResult<IEnumerable<LogErro>>> GetLogErros()
        {
            //throw new Exception("Parameter cannot be null");

            return await _context.LogErros.ToListAsync();
        }

        // GET: api/LogErros/5
        [HttpGet("{id}")]
        public async Task<ActionResult<LogErro>> GetLogErro(int id)
        {
            var logErro = await _context.LogErros.FindAsync(id);

            if (logErro == null)
            {
                return NotFound();
            }

            return logErro;
        }

        // PUT: api/LogErros/5
        // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
        [HttpPut("{id}")]
        public async Task<IActionResult> PutLogErro(int id, LogErro logErro)
        {
            if (id != logErro.Id)
            {
                return BadRequest();
            }

            _context.Entry(logErro).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException e)
            {
                var logRegister = new LogErro()
                {
                    Log_user_id = 0,
                    Log_uc = 0,
                    Log_uc_id = 0,
                    Log_projeto_id = 0,
                    Log_tabela = "Login",
                    Log_route = "Route",
                    Log_erro = e.Message
                };
                _context.LogErros.Add(logRegister);
                
                await _context.SaveChangesAsync();

                if (!LogErroExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/LogErros
        // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
        [HttpPost]
        public async Task<ActionResult<LogErro>> PostLogErro(LogErro logErro)
        {
            _context.LogErros.Add(logErro);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetLogErro", new { id = logErro.Id }, logErro);
        }

        // DELETE: api/LogErros/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteLogErro(int id)
        {
            var logErro = await _context.LogErros.FindAsync(id);
            if (logErro == null)
            {
                return NotFound();
            }

            _context.LogErros.Remove(logErro);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool LogErroExists(int id)
        {
            return _context.LogErros.Any(e => e.Id == id);
        }
    }
}
