﻿using baramaiamv.Models;
using BaramaiaMv.Database;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace baramaiamv.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ValuesController : ControllerBase
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly MvDbContext _mvDbContext;

        public ValuesController(SignInManager<ApplicationUser> signInManager,
            MvDbContext mvDbContext,
            UserManager<ApplicationUser> userManager)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _mvDbContext = mvDbContext;
        }

        [AllowAnonymous]
        [HttpPost("{email}/{password}")]
        public async Task<ActionResult> login2(String email, String password)
        {

            var user = _mvDbContext.Users.FirstOrDefault(x => x.Email == email);

            if (user != null)
            {
                var signInResult = await _signInManager.CheckPasswordSignInAsync(user, password, false);

                if (signInResult.Succeeded)
                    return Ok("Logado!");
                else
                    return BadRequest("Não logado");
            }
            else
            {
                BadRequest("Erro ao logar!");
            }

            return Ok("Erro ao logar!");
        }
    }
}
