﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using HerculesApi.Database;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using HerculesApi.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Microsoft.AspNetCore.Authorization;
using System.Globalization;

namespace HerculesApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class AdequacoesController : ControllerBase
    {
        private readonly HerculesDbContext _context;
        public AdequacoesController(HerculesDbContext context)
        {
            _context = context;
        }
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Adequacao>>> GetAdequacoes()
        {
            var adequacoes = _context.Adequacoes.ToList();
            if (adequacoes.Count > 0)
            {
                return adequacoes;
            }
            else
            {
                return NotFound("Não há lista de adequações");
            }
        }
        [HttpGet("{cliId}")]
        public async Task<ActionResult<Adequacao>> GetAdequacao(int cliId)
        {
            var adequacao = _context.Adequacoes.Where(x=>x.ade_cliId==cliId).FirstOrDefault();
            if (adequacao == null)
            {
                return NotFound("Empreiteira não encontrada");
            }

            return adequacao;
        }
        [HttpPost]
        public async Task<ActionResult<Adequacao>> PostAdequacoes(List<Adequacao> adequacoes)
        {
            var ade = _context.Adequacoes.AsNoTracking().ToList();

            if (ade.Count() > 0)
            {
                return BadRequest("Já existe lista anexada");
            }
            else
            {
                _context.Adequacoes.AddRange(adequacoes);
                await _context.SaveChangesAsync();

                return Ok("Lista de adequações inserida com sucesso!");
            }
        }
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteAdequacao(int id)
        {
            var adequacao = await _context.Adequacoes.FindAsync(id);
            if (adequacao == null)
            {
                return NotFound("Adequação não encontrada");
            }
            else
            {
                _context.Adequacoes.Remove(adequacao);
                await _context.SaveChangesAsync();

                return Ok("Adequação excluida com sucesso!");
            }

        }

        [HttpDelete]
        public async Task<ActionResult> Delete()
        {
            await _context.Database.ExecuteSqlRawAsync("TRUNCATE TABLE Adequacoes;");

            return Ok("Tabela limpa com sucesso!");
        }
        private bool AdequacaoExists(int id)
        {
            return _context.Adequacoes.Any(e => e.id == id);
        }
    }
}