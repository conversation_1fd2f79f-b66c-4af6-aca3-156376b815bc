﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HerculesApi.Models
{
    public class Foto
    {
        [Key]
        public int id { get; set; }
        public string foto_nome { get; set; }
        public string foto_extensao { get; set; }
        public byte[] foto_dado { get; set; }
        public string foto_tipo { get; set; }
        [NotMapped]
        public string foto_dado_base64 { get; set; }
    }
}
