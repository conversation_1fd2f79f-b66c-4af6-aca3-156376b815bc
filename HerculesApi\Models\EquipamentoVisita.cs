﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HerculesApi.Models
{
    public class EquipamentoVisita
    {
        [Key]
        public int id { get; set; }
        public string eqv_descricao { get; set; }
        public string? eqv_nmedidor { get; set; }
        public string? eqv_nserie { get; set; }
        public string? eqv_ndisplay { get; set; }
        [NotMapped]
        public Foto? foto { get; set; }
        [NotMapped]
        public int? visitaId { get; set; }
        [NotMapped]
        public bool instalado { get; set; }
    }
}
