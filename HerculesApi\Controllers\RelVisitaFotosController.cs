﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using HerculesApi.Database;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using HerculesApi.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Microsoft.AspNetCore.Authorization;

namespace HerculesApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class RelVisitaFotosController : ControllerBase
    {
        private readonly HerculesDbContext _context;
        public RelVisitaFotosController(HerculesDbContext context)
        {
            _context = context;
        }
        [HttpPost]
        public async Task<ActionResult<RelVisitaFoto>> PostRelVisitaFoto(List<RelVisitaFoto> relVisitaFoto)
        {
            _context.RelVisitasFotos.AddRange(relVisitaFoto);
            await _context.SaveChangesAsync();

            return Ok("Relacionamento salvo!");
        }
    }
}