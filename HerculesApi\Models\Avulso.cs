﻿﻿using DocumentFormat.OpenXml.Vml;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Reflection.Metadata.Ecma335;

namespace HerculesApi.Models
{
    public class Avulso
    {
        [Key]
        public int id { get; set; }
        public string avu_nome { get; set; }
        public int? avu_uc { get; set; }
        public string avu_logradouro { get; set; }
        public string avu_numero { get; set; }
        public string? avu_complemento { get; set; }
        public int? avu_cp { get; set; }
        public int? avu_et { get; set; }
        public int? avu_cs { get; set; }
        public string? avu_pos1 { get; set; }
        public string? avu_pos2 { get; set; }
        public string? avu_pos3 { get; set; }
        public int? avu_modulo { get; set; }
        public string? avu_display { get; set; }
        public int? avu_municipioId { get; set; }
        public int? avu_motivoId { get; set; }
        public string? avu_tecnico { get; set; }
        [NotMapped]
        public MotivoAvulso? motivo { get; set; }
        [NotMapped]
        public string? municipio { get; set; }
        public int? avu_nucleoId { get; set; }
        [NotMapped]
        public string? nucleo { get; set; }
        public int? avu_tanque { get; set; }
        public Avulso()
        {
            motivo = new MotivoAvulso();
        }
    }
}