﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using HerculesApi.Database;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using HerculesApi.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Microsoft.AspNetCore.Authorization;

namespace HerculesApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class TiposDeServico : ControllerBase
    {
        private readonly HerculesDbContext _context;
        public TiposDeServico(HerculesDbContext context)
        {
            _context = context;
        }
        [HttpGet]
        public async Task<ActionResult<IEnumerable<TipoServico>>> GetTiposServico()
        {
            var tiposServico = _context.TiposDeServico.ToList();
            if (tiposServico.Count > 0)
            {
                return tiposServico;
            }
            else
            {
                return NotFound("Não há tipos de serviço cadastrados");
            }
        }
        [HttpGet("{id}")]
        public async Task<ActionResult<TipoServico>> GetTipoServico(int id)
        {
            var tiposServico = await _context.TiposDeServico.FindAsync(id);
            if (tiposServico == null)
            {
                return NotFound("Tipo de serviço não encontrado");
            }

            return tiposServico;
        }
        [HttpPost]
        public async Task<ActionResult<TipoServico>> PostTipoServico(TipoServico tipoServico)
        {
            tipoServico.tps_descricao.ToUpper();
            var tps = _context.TiposDeServico
                .Where(x => x.tps_descricao.ToLower().Equals(tipoServico.tps_descricao.ToLower()))
                .AsNoTracking()
                .FirstOrDefault();
            if (tps != null)
            {
                return BadRequest(new { message = "Não é possível cadastrar tipo de serviço já cadastrado!" });
            }
            else
            {                
                _context.TiposDeServico.Add(tipoServico);
                await _context.SaveChangesAsync();

                return Ok("Tipo de serviço cadastrado com sucesso!");
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> PutTipoServico(int id, TipoServico tipoServico)
        {
            tipoServico.tps_descricao.ToUpper();
            var tps = _context.TiposDeServico.AsNoTracking().Where(x => x.id == id).FirstOrDefault();
            if (tps == null)
            {
                return BadRequest("Tipo de serviço não encontrado");
            }

            _context.Entry(tipoServico).State = EntityState.Modified;
            try
            {
                await _context.SaveChangesAsync();                
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!TipoServicoExists(id))
                {
                    return NotFound("Tipo de serviço não encontrado");
                }
                else
                {
                    throw;
                }
            }

            return Ok("Tipo de serviço alterado com sucesso!");
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteTipoServico(int id)
        {
            var tipoServico = await _context.TiposDeServico.FindAsync(id);
            if (tipoServico == null)
            {
                return NotFound("Tipo de serviço não encontrado");
            }
            else
            {
                _context.TiposDeServico.Remove(tipoServico);
                await _context.SaveChangesAsync();

                return Ok("Tipo de serviço excluido com sucesso!");
            }

        }
        private bool TipoServicoExists(int id)
        {
            return _context.TiposDeServico.Any(e => e.id == id);
        }
    }
}