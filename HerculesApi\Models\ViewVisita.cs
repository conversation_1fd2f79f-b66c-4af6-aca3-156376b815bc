﻿﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HerculesApi.Models
{
    [Table("vw_visitas")]
    public class ViewVisita
    {
        [Key]
        public int id { get; set; }
        public int visId { get; set; }
        
        // cliId é INT na PRODUÇÃO (que funciona)
        public int? cliId { get; set; }
        
        public DateTime? data { get; set; }
        public string? nome { get; set; }
        
        // Estes são INT no banco
        public int? cp { get; set; }
        public int? cs { get; set; }
        public int? et { get; set; }
        public int? efetivado { get; set; }
        
        // Estes são VARCHAR no banco (tamanhos conforme PRODUÇÃO)
        public string? municipio { get; set; }      // VARCHAR(100) na produção
        public string? nucleo { get; set; }         // VARCHAR(100) na produção
        public string? empreiteira { get; set; }    // VARCHAR(50) na produção
        public string? tipoServico { get; set; }    // VARCHAR(400)
        public string? tecnicos { get; set; }       // VARCHAR(100)
        
        // status é TINYINT no banco
        public byte? status { get; set; }
        
        [NotMapped]
        public List<OrdemVistoria> ordens { get; set; }
        
        // Propriedade auxiliar para conversão de status
        [NotMapped]
        public bool StatusBool => status == 1;
        
        public ViewVisita()
        {            
            ordens = new List<OrdemVistoria>();
        }
    }
}