﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HerculesApi.Models
{
    public class Visita
    {
        [Key]
        public int id { get; set; }
        public int? vis_ordId { get; set; }
        public DateTime vis_data { get; set; }
        public int vis_efetivado { get; set; }
        public int? vis_eqpVisitaId { get; set; }
        public string? vis_descricao { get; set; }
        public string? vis_problemas { get; set; }
        public string? vis_observacao { get; set; }
        public string? vis_gps { get; set; }
        public bool? vis_status { get; set; }
        [NotMapped]
        public int? avulsoId { get; set; }

        [NotMapped]
        public List<Foto> fotos { get; set; }
        [NotMapped]
        public List<EquipamentoVisita> eqpInstalados { get; set; }
        [NotMapped]
        public List<EquipamentoVisita> eqpRetirados { get; set; }
        [NotMapped]
        public Motivo motivo { get; set; }
        [NotMapped]
        public List<int> ordens { get; set; }

        public Visita()
        {
            fotos = new List<Foto>();
            eqpInstalados = new List<EquipamentoVisita>();
            eqpRetirados = new List<EquipamentoVisita>();
            motivo = new Motivo();
            ordens = new List<int>();
            
            // Valores padrão para evitar NullPointerException no Android
            vis_data = DateTime.Now;
            vis_efetivado = 0;
            vis_descricao = "";
            vis_problemas = "";
            vis_observacao = "";
            vis_gps = "";
            vis_status = true;
        }
    }


}