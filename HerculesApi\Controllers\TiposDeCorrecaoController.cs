﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using HerculesApi.Database;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using HerculesApi.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Microsoft.AspNetCore.Authorization;

namespace HerculesApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class TiposDeCorrecaoController : ControllerBase
    {
        private readonly HerculesDbContext _context;
        public TiposDeCorrecaoController(HerculesDbContext context)
        {
            _context = context;
        }
        [HttpGet]
        public async Task<ActionResult<IEnumerable<TipoCorrecao>>> GetTiposCorrecao()
        {
            var tpCorrecao = _context.TiposDeCorrecao.ToList();
            if (tpCorrecao.Count > 0)
            {
                return tpCorrecao;
            }
            else
            {
                return NotFound("Não há tipos de correção cadastrados");
            }
        }
        [HttpGet("{id}")]
        public async Task<ActionResult<TipoCorrecao>> GetTipoCorrecao(int id)
        {
            var tpCorrecao = await _context.TiposDeCorrecao.FindAsync(id);
            if (tpCorrecao == null)
            {
                return NotFound("Tipo de correção não encontrado");
            }

            return tpCorrecao;
        }
        [HttpPost]
        public async Task<ActionResult<TipoCorrecao>> PostTipoCorrecao(TipoCorrecao tpCorrecao)
        {
            tpCorrecao.tpc_descricao.ToUpper();
            var tpc = _context.TiposDeCorrecao
                .Where(x => x.tpc_descricao.ToLower().Equals(tpCorrecao.tpc_descricao.ToLower()))
                .AsNoTracking()
                .FirstOrDefault();
            if (tpc != null)
            {
                return BadRequest(new { message = "Não é possível cadastrar tipo de correção já cadastrado!" });
            }
            else
            {
                _context.TiposDeCorrecao.Add(tpCorrecao);
                await _context.SaveChangesAsync();

                return Ok("Tipo de correção cadastrado com sucesso!");
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> PutMotivo(int id, TipoCorrecao tpCorrecao)
        {
            tpCorrecao.tpc_descricao.ToUpper();
            var tpc = _context.TiposDeCorrecao.AsNoTracking().Where(x => x.id == id).FirstOrDefault();
            if (tpc == null)
            {
                return BadRequest("Tipo de correção não encontrado");
            }

            _context.Entry(tpCorrecao).State = EntityState.Modified;
            try
            {
                await _context.SaveChangesAsync();                
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!TipoExists(id))
                {
                    return NotFound("Tipo de correção não encontrado");
                }
                else
                {
                    throw;
                }
            }

            return Ok("Tipo de correção alterada com sucesso!");
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteTipoCorrecao(int id)
        {
            var tpCorrecao = await _context.TiposDeCorrecao.FindAsync(id);
            if (tpCorrecao == null)
            {
                return NotFound("Tipo de correção não encontrado");
            }
            else
            {
                _context.TiposDeCorrecao.Remove(tpCorrecao);
                await _context.SaveChangesAsync();

                return Ok("Tipo de correção excluido com sucesso!");
            }

        }
        private bool TipoExists(int id)
        {
            return _context.TiposDeCorrecao.Any(e => e.id == id);
        }
    }
}