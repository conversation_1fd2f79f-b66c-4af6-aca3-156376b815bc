﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace HerculesApi.Models
{
    public class Usuario
    {
        [Key]
        public int UserId { get; set; }
        public string Email { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string NickName { get; set; }
        public string PhoneNumber { get; set; }
        public string QrCode { get; set; }
        public string Matricula { get; set; }
        public bool Status { get; set; }
        public string Created { get; set; }
        public int ProjectId { get; set; }
        public int FuncaoId { get; set; }
        public string? EmailPessoal { get; set; }
    }
}

