﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using HerculesApi.Database;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using HerculesApi.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Microsoft.AspNetCore.Authorization;

namespace HerculesApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class MotivosController : ControllerBase
    {
        private readonly HerculesDbContext _context;
        public MotivosController(HerculesDbContext context)
        {
            _context = context;
        }
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Motivo>>> GetMotivos()
        {
            var motivos = _context.Motivos.Where(x=>x.mot_status==true).ToList();
            if (motivos.Count > 0)
            {
                return motivos;
            }
            else
            {
                return NotFound("Não há itens em motivos");
            }
        }
        [HttpGet ("web")]
        public async Task<ActionResult<IEnumerable<Motivo>>> GetMotivosWeb()
        {
            var motivos = _context.Motivos.ToList();
            if (motivos.Count > 0)
            {
                return motivos;
            }
            else
            {
                return NotFound("Não há itens em motivos");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<Motivo>> GetMotivo(int id)
        {
            var motivos = await _context.Motivos.FindAsync(id);
            if (motivos == null)
            {
                return NotFound("Motivo não encontrado");
            }

            return motivos;
        }
        [HttpPost]
        public async Task<ActionResult<Motivo>> PostMotivo(Motivo motivo)
        {
            
            var mot = _context.Motivos
                .Where(x => x.mot_descricao.ToLower().Equals(motivo.mot_descricao.ToLower()))
                .AsNoTracking()
                .FirstOrDefault();
            if (mot != null)
            {
                return BadRequest(new { message = "Não é possível cadastrar motivo já cadastrado!" });
            }
            else
            {
                motivo.mot_descricao = motivo.mot_descricao.ToLower();
                _context.Motivos.Add(motivo);
                await _context.SaveChangesAsync();

                return Ok("Motivo cadastrado com sucesso!");
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> PutMotivo(int id, Motivo motivo)
        {
            var mot = _context.Motivos.AsNoTracking().Where(x => x.id == id).FirstOrDefault();
            if (mot == null)
            {
                return BadRequest("Motivo não encontrado");
            }

            _context.Entry(motivo).State = EntityState.Modified;
            try
            {
                await _context.SaveChangesAsync();                
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!MotivosExists(id))
                {
                    return NotFound("Motivo não encontrado");
                }
                else
                {
                    throw;
                }
            }

            return Ok("Motivo alterado com sucesso!");
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteMotivo(int id)
        {
            var motivo = await _context.Motivos.FindAsync(id);
            if (motivo == null)
            {
                return NotFound("Motivo não encontrado");
            }
            else
            {
                _context.Motivos.Remove(motivo);
                await _context.SaveChangesAsync();

                return Ok("Motivo excluido com sucesso!");
            }

        }
        private bool MotivosExists(int id)
        {
            return _context.Motivos.Any(e => e.id == id);
        }
    }
}