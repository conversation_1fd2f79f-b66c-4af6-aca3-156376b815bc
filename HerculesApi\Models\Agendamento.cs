﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HerculesApi.Models
{
    public class Agendamento
    {
        [Key]
        public int id { get; set; }
        public int? agd_cliId { get; set; }
        public string? agd_cliNome { get; set; }
        public int? agd_modulo { get; set; }
        public string? agd_cliTelefone { get; set; }
        public int? agd_nucleoId { get; set; }
        public int? agd_municipioId { get; set; }
        public string? agd_endereco { get; set; }
        public DateTime? agd_dtVisita { get; set; }
        public string? agd_obs { get; set; }
        public int? agd_status { get; set; }
        [NotMapped]
        public List<int>? userIds { get; set; }
        [NotMapped]
        public List<int>? tpServIds { get; set; }
        public Agendamento()
        {
            userIds = new List<int>();
            tpServIds = new List<int>();
        }
    }
}
