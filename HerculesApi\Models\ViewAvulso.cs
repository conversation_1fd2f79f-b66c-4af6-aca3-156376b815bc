﻿namespace HerculesApi.Models
{
    public class ViewAvulso
    {
        public int id { get; set; }
        public string nome { get; set; }
        public int? uc { get; set; }
        public string logradouro { get; set; }
        public string numero { get; set; }
        public string? complemento { get; set; }
        public int? cp { get; set; }
        public int? et { get; set; }
        public int? cs { get; set; }
        public string? pos1 { get; set; }
        public string? pos2 { get; set; }
        public string? pos3 { get; set; }
        public int? modulo { get; set; }
        public string? display { get; set; }
        public string? municipio { get; set; }        
        public string? nucleo { get; set; }
        public int? tanque { get; set; }
        public string? gps { get; set; }
        public string? motivo { get; set; }
        public string? tecnico { get; set; }
    }
}
