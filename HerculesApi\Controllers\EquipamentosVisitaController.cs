﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using HerculesApi.Database;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using HerculesApi.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Microsoft.AspNetCore.Authorization;
using NPOI.SS.Formula.Functions;
using System.ComponentModel;

namespace HerculesApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class EquipamentosVisitaController : ControllerBase
    {
        private readonly HerculesDbContext _context;
        public EquipamentosVisitaController(HerculesDbContext context)
        {
            _context = context;
        }
        
        [HttpPut("{id}")]
        public async Task<IActionResult> PutEquipamentoVisita(int id, EquipamentoVisita equipamentoVisita)
        {
            var equ = _context.EquipamentosVisitas.Where(x => x.id == id).FirstOrDefault();
            if (equ == null)
            {
                return BadRequest("Equipamento não encontrado");
            }

            _context.Entry(equipamentoVisita).State = EntityState.Modified;
            try
            {
                await _context.SaveChangesAsync();                
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!EquipamentoExists(id))
                {
                    return NotFound("Equipamento não encontrado");
                }
                else
                {
                    throw;
                }
            }

            return Ok("Equipamento alterado com sucesso!");
        }
        [HttpGet("{id}")]
        public async Task<ActionResult<EquipamentoVisita>> GetEquipamento(int id)
        {
            var equipamento = await _context.EquipamentosVisitas.FindAsync(id);
            if (equipamento == null)
            {
                return NotFound("Equipamento não encontrado");
            }
            var relFoto = _context.RelEquipVisitaFotos.AsNoTracking().Where(x => x.fotoId == id).FirstOrDefault();
            var foto = _context.Fotos.Where(f => f.id == relFoto.fotoId).FirstOrDefault();

            equipamento.foto = foto;

            return equipamento;
        }
        [HttpPost]
        public async Task<ActionResult<EquipamentoVisita>> PostEquipamentoVisita(List<EquipamentoVisita> equipamentos)
        {
            if (equipamentos==null)
            {
                return BadRequest(new { message = "Lista de equipamentos vazia!" });
            }
            else
            {
                foreach (var item in equipamentos)
                {
                    EquipamentoVisita eqpVisita = new EquipamentoVisita();
                    eqpVisita.eqv_descricao = item.eqv_descricao;
                    eqpVisita.eqv_nmedidor = item.eqv_nmedidor;
                    eqpVisita.eqv_nserie = item.eqv_nserie;
                    eqpVisita.eqv_ndisplay = item.eqv_ndisplay;

                    _context.EquipamentosVisitas.Add(eqpVisita);
                    await _context.SaveChangesAsync();

                    if (item.foto != null)
                    {
                        if (!string.IsNullOrEmpty(item.foto.foto_dado_base64))
                        {
                            item.foto.foto_dado = Convert.FromBase64String(item.foto.foto_dado_base64);
                        }
                        _context.Fotos.Add(item.foto);
                        await _context.SaveChangesAsync();

                        RelEquipVisitaFoto r = new RelEquipVisitaFoto();
                        r.visitaId = Convert.ToInt32(item.visitaId);
                        r.equipId = eqpVisita.id;
                        r.fotoId = item.foto.id;

                        _context.RelEquipVisitaFotos.Add(r);
                        await _context.SaveChangesAsync();
                    }
                    RelVisitaEquipamento releqp = new RelVisitaEquipamento();
                    releqp.eqpId = eqpVisita.id;
                    releqp.visId = Convert.ToInt32(item.visitaId);
                    releqp.situacao = item.instalado;

                    _context.RelVisitasEquipamentos.Add(releqp);
                    await _context.SaveChangesAsync();
                }
                return Ok("Lista de equipamentos adicionada com sucesso!");
            }
        }
        private bool EquipamentoExists(int id)
        {
            return _context.EquipamentosVisitas.Any(e => e.id == id);
        }
    }
}