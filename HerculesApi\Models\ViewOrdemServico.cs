﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;

namespace HerculesApi.Models
{
    public class ViewOrdemServico
    {
        [Key]
        public int id { get; set; }
        public string? ord_ordem { get; set; }
        public int? ord_cliId { get; set; }
        public int? ord_uc { get; set; }
        public int? ord_na { get; set; }
        public string? ord_nome { get; set; }
        public string? ord_municipio { get; set; }
        public string? ord_nucleo { get; set; }
        public string? ord_logradouro { get; set; }
        public string? ord_numero { get; set; }
        public string? ord_complemento { get; set; }
        public string? ord_bloco { get; set; }
        public int? ord_cp { get; set; }
        public int? ord_cs { get; set; }
        public string? ord_pos1 { get; set; }
        public string? ord_pos2 { get; set; }
        public string? ord_pos3 { get; set; }
        public int? ord_et { get; set; }
        public int? ord_modulo { get; set; }
        public string? ord_display { get; set; }
        public string? ord_motivo { get; set; }
        public string? ord_empreiteira { get; set; }
        public string? ord_status_uc { get; set; }
        public string? ord_situacao { get; set; }
        public string? ord_gps { get; set; }
        public DateTime? ord_dataEntrada { get; set; }
        public DateTime? ord_dataFechamento { get; set; }
        public int ord_status { get; set; }
        public string? ord_obs { get; set; }
        public string? ord_tecnicos { get; set; }
        [NotMapped]
        public int? totalOrdens { get; set; }
        public string? motivo { get; set; }
        [NotMapped]
        public string? ord_tipoServ { get; set; }
        [NotMapped]
        public string? tecnicos { get; set; }
        [NotMapped]
        public List<int>? userId { get; set; }
        public ViewOrdemServico()
        {
            userId = new List<int>();
        }
    }
}
