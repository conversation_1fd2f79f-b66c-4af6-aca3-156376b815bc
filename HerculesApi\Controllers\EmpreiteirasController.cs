﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using HerculesApi.Database;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using HerculesApi.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Microsoft.AspNetCore.Authorization;

namespace HerculesApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class EmpreiteirasController : ControllerBase
    {
        private readonly HerculesDbContext _context;
        public EmpreiteirasController(HerculesDbContext context)
        {
            _context = context;
        }
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Empreiteira>>> GetEmpreiteiras()
        {
            var empreiteiras = _context.Empreiteiras.ToList();
            if (empreiteiras.Count > 0)
            {
                return empreiteiras;
            }
            else
            {
                return NotFound("Não há empreiteiras");
            }
        }
        [HttpGet("{id}")]
        public async Task<ActionResult<Empreiteira>> GetEmpreiteira(int id)
        {
            var empreiteira = await _context.Empreiteiras.FindAsync(id);
            if (empreiteira == null)
            {
                return NotFound("Empreiteira não encontrada");
            }

            return empreiteira;
        }
        [HttpPost]
        public async Task<ActionResult<Empreiteira>> PostEmpreiteira(Empreiteira empreiteira)
        {
            empreiteira.emp_desc.ToUpper();
            var emp = _context.Empreiteiras
                .Where(x => x.emp_desc.ToLower().Equals(empreiteira.emp_desc.ToLower()))
                .AsNoTracking()
                .FirstOrDefault();
            if (emp != null)
            {
                return BadRequest(new { message = "Não é possível cadastrar empreiteira já cadastrada!" });
            }
            else
            {
                _context.Empreiteiras.Add(empreiteira);
                await _context.SaveChangesAsync();

                return Ok("Empreiteira cadastrada com sucesso!");
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> PutEmpreiteira(int id, Empreiteira empreiteira)
        {
            empreiteira.emp_desc.ToUpper();
            var emp = _context.Empreiteiras.Where(x => x.id == id).AsNoTracking().FirstOrDefault();
            if (emp == null)
            {
                return BadRequest("Empreiteira não encontrada");
            }

            _context.Entry(empreiteira).State = EntityState.Modified;
            try
            {
                await _context.SaveChangesAsync();                
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!EmpreiteiraExists(id))
                {
                    return NotFound("Empreiteira não encontrada");
                }
                else
                {
                    throw;
                }
            }

            return Ok("Empreiteira alterada com sucesso!");
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteEmpreiteira(int id)
        {
            var empreiteira = await _context.Empreiteiras.FindAsync(id);
            if (empreiteira == null)
            {
                return NotFound("Empreiteira não encontrada");
            }
            else
            {
                _context.Empreiteiras.Remove(empreiteira);
                await _context.SaveChangesAsync();

                return Ok("Empreiteira excluida com sucesso!");
            }

        }
        private bool EmpreiteiraExists(int id)
        {
            return _context.Empreiteiras.Any(e => e.id == id);
        }
    }
}