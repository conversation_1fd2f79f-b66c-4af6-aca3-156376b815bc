﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using HerculesApi.Database;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using HerculesApi.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Microsoft.AspNetCore.Authorization;

namespace HerculesApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class MunicipiosController : ControllerBase
    {
        private readonly HerculesDbContext _context;
        public MunicipiosController(HerculesDbContext context)
        {
            _context = context;
        }
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Municipio>>> GetMunicipios()
        {
            var municipios = _context.Municipios.OrderBy(x => x.mun_descricao).ToList();
            if (municipios.Count > 0)
            {
                return municipios;
            }
            else
            {
                return NotFound("Não há municipios");
            }
        }
        [HttpGet("gerais")]
        public async Task<ActionResult<IEnumerable<ViewMunicipio>>> GetMunicipiosGerais()
        {
            var municipios = _context.vw_municipios.OrderBy(x=>x.municipio).ToList();
            if (municipios.Count > 0)
            {
                foreach (var item in municipios)
                {
                    item.totalServicos = item.qtdCorrecao + item.qtdRetirada + item.qtdProvisorio;
                }
                return municipios;
            }
            else
            {
                return NotFound("Não há municipios");
            }
        }

        [HttpGet("nucleo/{nucleoid}")]
        public async Task<ActionResult<IEnumerable<Object>>> GetMunicipiosNucleo(int nucleoid)
        {
            var nucleo = _context.Nucleos.Where(x => x.id == nucleoid).FirstOrDefault();
            if (nucleo == null)
            {
                return NotFound("Núcleo não existente!");
            }

            var listaMunicipios = await (from m in _context.Municipios
                                         join rel in _context.RelMunicipiosNucleos
                                         on m.id equals rel.munId
                                         where rel.nucId == nucleoid
                                         select new
                                         {
                                             m.id,
                                             m.mun_descricao
                                         })
                                         .OrderBy(m => m.mun_descricao)
                                         .ToListAsync();
            if (listaMunicipios.Count > 0)
            {
                return listaMunicipios;
            }
            else
            {
                return NotFound("Não há municipios");
            }
        }
        [HttpGet("servico")]
        public async Task<ActionResult<IEnumerable<Object>>> GetMunicipiosServico()
        {
            var listaMunicipios = await (from m in _context.Municipios
                                         join rel in _context.RelMunicipiosNucleos
                                         on m.id equals rel.munId
                                         join vw in _context.vw_municipios
                                         on m.id equals vw.id
                                         select new
                                         {
                                             id = m.id,
                                             mun_descricao = m.mun_descricao,
                                             mun_correcao = vw.qtdCorrecao,
                                             mun_provisorio = vw.qtdProvisorio,
                                             mun_retirada = vw.qtdRetirada
                                         })
                                         .Distinct() // Remove duplicatas
                                         .OrderBy(m => m.mun_descricao)
                                         .ToListAsync();
            if (listaMunicipios.Count > 0)
            {
                return listaMunicipios;
            }
            else
            {
                return NotFound("Não há municípios");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<Municipio>> GetMunicipio(int id)
        {
            var municipio = await _context.Municipios.FindAsync(id);
            if (municipio == null)
            {
                return NotFound("Municipio não encontrado");
            }

            return municipio;
        }
        [HttpPost]
        public async Task<ActionResult<Municipio>> PostMunicipio(Municipio municipio)
        {
            municipio.mun_descricao.ToUpper();
            var mun = _context.Municipios
                .Where(x => x.mun_descricao.Equals(municipio.mun_descricao))
                .AsNoTracking()
                .FirstOrDefault();
            if (mun != null)
            {
                return BadRequest(new { message = "Não é possível cadastrar municipio já cadastrado!" });
            }
            else
            {
                _context.Municipios.Add(municipio);
                await _context.SaveChangesAsync();

                return Ok("Municipio cadastrado com sucesso!");
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> PutMunicipio(int id, Municipio municipio)
        {
            municipio.mun_descricao.ToUpper();
            var mun = _context.Municipios.AsNoTracking().Where(x => x.id == id).FirstOrDefault();
            if (mun == null)
            {
                return BadRequest("Municipio não encontrado");
            }

            _context.Entry(municipio).State = EntityState.Modified;
            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!MunicipioExists(id))
                {
                    return NotFound("Municipio não encontrado");
                }
                else
                {
                    throw;
                }
            }

            return Ok("Municipio alterado com sucesso!");
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteMunicipio(int id)
        {
            var municipio = await _context.Municipios.FindAsync(id);
            if (municipio == null)
            {
                return NotFound("Município não encontrado");
            }

            var listRel = await _context.RelMunicipiosNucleos
                                         .Where(x => x.munId == id)
                                         .ToListAsync();

            if (listRel.Any())
            {
                var listNucleoIds = listRel.Select(x => x.nucId).ToList();
                var listNucleos = await _context.Nucleos
                                                .Where(n => listNucleoIds.Contains(n.id))
                                                .ToListAsync();

                if (listNucleos.Any())
                {
                    _context.Nucleos.RemoveRange(listNucleos);
                }

                _context.RelMunicipiosNucleos.RemoveRange(listRel);
            }

            _context.Municipios.Remove(municipio);

            await _context.SaveChangesAsync();

            return Ok("Município e relacionamentos excluídos com sucesso!");
        }

        private bool MunicipioExists(int id)
        {
            return _context.Municipios.Any(e => e.id == id);
        }
    }
}