﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace HerculesApi.Models
{
    public class LogErro
    {
        [Key]
        public int Id { get; set; }
        public int Log_user_id { get; set; }
        public int Log_uc { get; set; }
        public int Log_uc_id { get; set; }
        public int Log_projeto_id { get; set; }
        public string Log_tabela { get; set; }
        public string Log_route { get; set; }
        public string Log_erro { get; set; }
        public string Created { get; set; } = DateTime.Now.AddHours(-3).ToString();
    }
}

