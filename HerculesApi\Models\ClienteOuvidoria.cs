﻿namespace HerculesApi.Models
{
    public class ClienteOuvidoria
    {
        public string? ouv_logradouro { get; set; }
        public string? ouv_numero { get; set; }
        public string? ouv_complemento { get; set; }
        public int? ouv_cp { get; set; }
        public int? ouv_uc { get; set; }
        public string? ouv_titular { get; set; }
        public int? ouv_modulo { get; set; }
        public string? ouv_display { get; set; }
        public int? ouv_et { get; set; }
        public int? ouv_cs { get; set; }
        public string? ouv_pos1 { get; set; }
        public string? ouv_pos2 { get; set; }
        public string? ouv_pos3 { get; set; }
        public string? ouv_observacao { get; set; }
    }
}
