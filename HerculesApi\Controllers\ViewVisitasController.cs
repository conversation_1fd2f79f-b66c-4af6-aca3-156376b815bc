﻿﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using HerculesApi.Database;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using HerculesApi.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Microsoft.AspNetCore.Authorization;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.Extensions.Hosting.Internal;
using System.Globalization;
using System.IO;
using System.Threading;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using Microsoft.IdentityModel.Tokens;

namespace HerculesApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class ViewVisitasController : ControllerBase
    {
        private readonly HerculesDbContext _context;
        private readonly AdmDbContext _contextAdm;
        private readonly Microsoft.AspNetCore.Hosting.IHostingEnvironment hostingEnvironment;
        public ViewVisitasController(HerculesDbContext context, AdmDbContext contextAdm)
        {
            _context = context;
            _contextAdm = contextAdm;
        }
        [HttpGet]
        public async Task<ActionResult<IEnumerable<ViewVisita>>> GetVisitas()
        {
            // Busca visitas da view padrão
            var visitasView = _context.vw_visitas.ToList();
            
            // Busca visitas de avulsos que não estão na view
            var visitasAvulsos = (from v in _context.Visitas
                                 join ra in _context.RelVisitasAvulsos on v.id equals ra.visId
                                 join a in _context.vw_avulsos on ra.avulsoId equals a.id
                                 where !_context.vw_visitas.Any(vv => vv.visId == v.id) // Evita duplicatas
                                 select new ViewVisita
                                 {
                                     id = v.id,
                                     visId = v.id,
                                     cliId = 0, // Avulsos não têm cliente
                                     data = v.vis_data,
                                     nome = a.nome,
                                     cp = a.cp,
                                     cs = a.cs,
                                     et = a.et,
                                     efetivado = v.vis_efetivado,
                                     municipio = a.municipio,
                                     nucleo = a.nucleo,
                                     empreiteira = "",
                                     tipoServico = "Avulso",
                                     tecnicos = a.tecnico,
                                     status = (byte?)(v.vis_status == true ? 1 : 0)
                                 }).ToList();

            var todasVisitas = visitasView.Concat(visitasAvulsos).ToList();
            
            if (todasVisitas.Count > 0)
            {
                return todasVisitas;
            }
            else
            {
                return NotFound("Não há visitas");
            }
        }


        //[HttpGet("visitas/periodo/{dtinicial}/{dtfinal}")]
        ////[Authorize]
        //public async Task<ActionResult<IEnumerable<ViewVisita>>> GetVisitasPeriodo(DateTime dtinicial, DateTime dtfinal)
        //{


        //    dtinicial = new DateTime(dtinicial.Year, dtinicial.Month, dtinicial.Day, 0, 1, 0);
        //    dtfinal = new DateTime(dtfinal.Year, dtfinal.Month, dtfinal.Day, 23, 59, 0);

        //    var visitas = await _context.vw_visitas
        //                .Where(v => v.data >= dtinicial && v.data <= dtfinal)
        //                .Distinct()
        //                .ToListAsync();

        //    if (User.IsInRole("Adm"))
        //    {
        //        visitas.Where(x => x.status == true);
        //    }
        //    if (visitas != null)
        //    {

        //        foreach (var item in visitas)
        //        {
        //            List<OrdemVistoria> listaOrd = new List<OrdemVistoria>();
        //            var rel = _context.RelVisitasOrdens.AsNoTracking().Where(x => x.visId == item.visId).ToList();

        //            var idOrdens = rel.Select(x => x.ordemId).ToList();
        //            var ordens = _context.OrdensServico.Where(x => idOrdens.Contains(x.id)).ToList();

        //            foreach (var o in ordens)
        //            {
        //                OrdemVistoria ord = new OrdemVistoria();
        //                ord.numOrdem = o.ord_ordem;
        //                ord.cliId = Convert.ToInt32(o.ord_cliId);
        //                ord.nomeCli = o.ord_nome;

        //                // Suas regras de tipoServico...
        //                listaOrd.Add(ord);

        //                //var tecnicosIds = await _context.RelOrdensTecnicos
        //                //    .AsNoTracking()
        //                //    .Where(x => x.ordemId == o.id)
        //                //    .Select(x => x.tecId)
        //                //    .Distinct()
        //                //    .ToListAsync();

        //                //var users = await _contextAdm.Users
        //                //    .Where(u => tecnicosIds.Contains(u.UserId))
        //                //    .Select(u => new
        //                //    {
        //                //        u.UserId,
        //                //        u.FirstName,
        //                //        u.LastName
        //                //    })
        //                //    .ToListAsync();

        //                // Garante que a lista de usuários existe
        //                //item.usuarios ??= new List<UsuarioViewModel>();
        //                item.usuarios = o.ord_tecnicos;

        //                // Usa um HashSet para evitar duplicatas
        //                //var existingUserIds = new HashSet<int>(item.usuarios.Select(u => u.userId));

        //                //foreach (var user in users)
        //                //{
        //                //    if (!existingUserIds.Contains(user.UserId))
        //                //    {
        //                //        UsuarioViewModel u = new UsuarioViewModel();
        //                //        u.userId = user.UserId;
        //                //        u.Name = user.FirstName + " " + user.LastName;

        //                //        item.usuarios.Add(u);
        //                //        existingUserIds.Add(user.UserId); // Adiciona ao HashSet para evitar repetição
        //                //    }
        //                //}
        //            }

        //            item.ordens = listaOrd;
        //        }
        //    }

        //    return Ok(visitas.OrderByDescending(x => x.visId));
        //}
        [HttpGet("visitas/periodo/{dtinicial}/{dtfinal}")]
        //[Authorize]
        public async Task<ActionResult<IEnumerable<ViewVisita>>> GetVisitasPeriodo(DateTime dtinicial, DateTime dtfinal)
        {


            dtinicial = new DateTime(dtinicial.Year, dtinicial.Month, dtinicial.Day, 0, 1, 0);
            dtfinal = new DateTime(dtfinal.Year, dtfinal.Month, dtfinal.Day, 23, 59, 0);

            // Busca visitas da view padrão no período
            var visitas = await _context.vw_visitas
                        .Where(v => v.data >= dtinicial && v.data <= dtfinal)
                        .Distinct()
                        .ToListAsync();

            // Busca visitas de avulsos no período
            var visitasAvulsos = await (from v in _context.Visitas
                                       join ra in _context.RelVisitasAvulsos on v.id equals ra.visId
                                       join a in _context.vw_avulsos on ra.avulsoId equals a.id
                                       where v.vis_data >= dtinicial && v.vis_data <= dtfinal
                                       && !_context.vw_visitas.Any(vv => vv.visId == v.id) // Evita duplicatas
                                       select new ViewVisita
                                       {
                                           id = v.id,
                                           visId = v.id,
                                           cliId = 0, // Avulsos não têm cliente
                                           data = v.vis_data,
                                           nome = a.nome,
                                           cp = a.cp,
                                           cs = a.cs,
                                           et = a.et,
                                           efetivado = v.vis_efetivado,
                                           municipio = a.municipio,
                                           nucleo = a.nucleo,
                                           empreiteira = "",
                                           tipoServico = "Avulso",
                                           tecnicos = a.tecnico,
                                           status = (byte?)(v.vis_status == true ? 1 : 0)
                                       }).ToListAsync();

            // Combina as duas listas
            visitas.AddRange(visitasAvulsos);

            if (User.IsInRole("Adm"))
            {
                visitas = visitas.Where(x => x.status == 1).ToList();
            }
            if (visitas != null)
            {

                foreach (var item in visitas)
                {
                    List<OrdemVistoria> listaOrd = new List<OrdemVistoria>();
                    var rel = _context.RelVisitasOrdens.AsNoTracking().Where(x => x.visId == item.visId).ToList();

                    var idOrdens = rel.Select(x => x.ordemId).ToList();
                    var ordens = _context.OrdensServico.Where(x => idOrdens.Contains(x.id)).ToList();

                    foreach (var o in ordens)
                    {
                        OrdemVistoria ord = new OrdemVistoria();
                        ord.numOrdem = o.ord_ordem;
                        ord.cliId = o.ord_cliId ?? 0;
                        ord.nomeCli = o.ord_nome;

                        // Suas regras de tipoServico...
                        listaOrd.Add(ord);
                        
                        item.tecnicos = o.ord_tecnicos;
                        //var tecnicosIds = await _context.RelOrdensTecnicos
                        //    .AsNoTracking()
                        //    .Where(x => x.ordemId == o.id)
                        //    .Select(x => x.tecId)
                        //    .Distinct()
                        //    .ToListAsync();

                        //var users = await _contextAdm.Users
                        //    .Where(u => tecnicosIds.Contains(u.UserId))
                        //    .Select(u => new
                        //    {
                        //        u.UserId,
                        //        u.FirstName,
                        //        u.LastName
                        //    })
                        //    .ToListAsync();

                        //// Garante que a lista de usuários existe
                        //item.usuarios ??= new List<UsuarioViewModel>();

                        //// Usa um HashSet para evitar duplicatas
                        //var existingUserIds = new HashSet<int>(item.usuarios.Select(u => u.userId));

                        //foreach (var user in users)
                        //{
                        //    if (!existingUserIds.Contains(user.UserId))
                        //    {
                        //        UsuarioViewModel u = new UsuarioViewModel();
                        //        u.userId = user.UserId;
                        //        u.Name = user.FirstName + " " + user.LastName;

                        //        item.usuarios.Add(u);
                        //        existingUserIds.Add(user.UserId); // Adiciona ao HashSet para evitar repetição
                        //    }
                        //}
                    }

                    item.ordens = listaOrd;
                }
            }

            return Ok(visitas.OrderByDescending(x => x.visId));
        }
    }
}