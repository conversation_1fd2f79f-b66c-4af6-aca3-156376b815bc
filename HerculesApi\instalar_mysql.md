# Como Instalar/Configurar MySQL para usar mysqldump

## Opção 1: Usar MySQL já instalado (se você tem MySQL Workbench)

Se você já tem o MySQL Workbench, provavelmente o MySQL Server também está instalado. Execute o script:

```cmd
configurar_mysql.bat
```

Este script vai:
1. Procurar o MySQL nos locais comuns
2. Testar a conexão
3. Fazer o backup automaticamente
4. Copiar para o banco de teste

## Opção 2: Instalar MySQL Community Server

1. **Baixar MySQL**: https://dev.mysql.com/downloads/mysql/
2. **Instalar** com as opções padrão
3. **Adicionar ao PATH** (durante a instalação marque esta opção)
4. **Executar** o script `copiar_banco_mysqldump.bat`

## Opção 3: Usar XAMPP/WAMP/Laragon

Se você usa algum destes ambientes de desenvolvimento:

### XAMPP:
```cmd
C:\xampp\mysql\bin\mysqldump.exe -h *************** -u erickson -p"B@r@m@i@0824*" bd_hercules > backup_producao.sql
```

### WAMP:
```cmd
C:\wamp64\bin\mysql\mysql8.0.21\bin\mysqldump.exe -h *************** -u erickson -p"B@r@m@i@0824*" bd_hercules > backup_producao.sql
```

### Laragon:
```cmd
C:\laragon\bin\mysql\mysql-8.0.30-winx64\bin\mysqldump.exe -h *************** -u erickson -p"B@r@m@i@0824*" bd_hercules > backup_producao.sql
```

## Opção 4: Adicionar MySQL ao PATH manualmente

1. **Encontre** a pasta `bin` do MySQL (geralmente em `C:\Program Files\MySQL\MySQL Server X.X\bin`)
2. **Copie** o caminho completo
3. **Adicione ao PATH**:
   - Pressione `Win + R`, digite `sysdm.cpl`
   - Aba "Avançado" → "Variáveis de Ambiente"
   - Em "Variáveis do Sistema", encontre "Path" e clique "Editar"
   - Clique "Novo" e cole o caminho da pasta `bin`
   - Clique "OK" em todas as janelas
4. **Reinicie** o Prompt de Comando
5. **Execute** `copiar_banco_mysqldump.bat`

## Verificar se funcionou

Após qualquer opção, teste no cmd:
```cmd
mysqldump --version
```

Se aparecer a versão, está funcionando!

## Comandos manuais (se preferir)

```cmd
# 1. Backup
mysqldump -h *************** -u erickson -p"B@r@m@i@0824*" bd_hercules > backup_producao.sql

# 2. Recriar banco teste
mysql -h *************** -u erickson -p"B@r@m@i@0824*" -e "DROP DATABASE IF EXISTS bd_hercules_teste;"
mysql -h *************** -u erickson -p"B@r@m@i@0824*" -e "CREATE DATABASE bd_hercules_teste;"

# 3. Importar
mysql -h *************** -u erickson -p"B@r@m@i@0824*" bd_hercules_teste < backup_producao.sql
```