﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using HerculesApi.Database;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using HerculesApi.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.IO;
using Microsoft.AspNetCore.Authorization;

namespace HerculesApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class ArquivosController : ControllerBase
    {
        private readonly HerculesDbContext _context;
        public ArquivosController(HerculesDbContext context)
        {
            _context = context;
        }
        //[HttpGet("ouvidoria/{na}")]
        //public async Task<ActionResult<Arquivo>> GetArquivoOuvidoria(int na)
        //{
        //    var relacao = _context.RelArquivosOuvidorias.AsNoTracking().Where(x => x.ouvNa == na).FirstOrDefault();
        //    var arquivo = _context.Arquivos.AsNoTracking().Where(y => y.id == relacao.arqId).FirstOrDefault();
        //    if (arquivo == null)
        //    {
        //        return NotFound("Arquivo não encontrado não encontrado");
        //    }
        //    return arquivo;
        //}
        [HttpGet("ouvidoria/{na}")]
        public async Task<IActionResult> GetArquivoOuvidoria(int na)
        {
            var relacao = _context.RelArquivosOuvidorias.AsNoTracking().FirstOrDefault(x => x.ouvNa == na);
            if (relacao == null)
            {
                return NotFound("Relação não encontrada");
            }

            var arquivo = _context.Arquivos.AsNoTracking().FirstOrDefault(y => y.id == relacao.arqId);
            if (arquivo == null)
            {
                return NotFound("Arquivo não encontrado");
            }
            
            var contentType = arquivo.arq_tipo switch
            {
                ".jpg" => "image/jpeg",
                ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".pdf" => "application/pdf",
                ".txt" => "text/plain",
                _ => "application/octet-stream" // Tipo genérico para arquivos desconhecidos
            };
            return File(arquivo.arq_dado, arquivo.arq_extensao, arquivo.arq_nome);
        }

        [HttpPost("upload/{tipo}/{id}")]
        public async Task<IActionResult> UploadArquivo(IFormFile file, [FromForm] string tipo, int id)
        {
            tipo.ToUpper();
            if (file == null || file.Length == 0)
                return BadRequest("Arquivo não enviado.");

            // Obtém o nome e a extensão do arquivo
            var arqNome = Path.GetFileNameWithoutExtension(file.FileName);
            var arqExtensao = Path.GetExtension(file.FileName);

            // Converte o arquivo para um array de bytes (blob)
            byte[] arqDado;
            using (var ms = new MemoryStream())
            {
                await file.CopyToAsync(ms);
                arqDado = ms.ToArray();
            }

            // Cria um novo objeto Arquivo com os dados do upload
            var arquivo = new Arquivo
            {
                arq_nome = arqNome,
                arq_extensao = arqExtensao,
                arq_dado = arqDado,
                arq_tipo = tipo // Pode ser "foto", "ouvidoria", etc.
            };

            // Salva no banco de dados
            _context.Arquivos.Add(arquivo);
            await _context.SaveChangesAsync();
            if (tipo.Equals("AGENDAMENTO"))
            {
                RelArquivoAgend relacionamento = new RelArquivoAgend();
                relacionamento.arqId = arquivo.id;
                relacionamento.agendId = id;
            }

            return Ok(new { id = arquivo.id, nome = arquivo.arq_nome });
        }        
        [HttpPost("upload/ouvidoria")]
        public async Task<IActionResult> UploadArquivoOuvidoria(IFormFile file, [FromForm] int na)
        {
            if (file == null || file.Length == 0)
                return BadRequest("Arquivo não enviado.");

            using var ms = new MemoryStream();
            await file.CopyToAsync(ms);
            
            var arquivo = new Arquivo
            {
                arq_nome = Path.GetFileNameWithoutExtension(file.FileName),
                arq_extensao = Path.GetExtension(file.FileName),
                arq_dado = ms.ToArray(),
                arq_tipo = "OUVIDORIA"
            };
            
            _context.Arquivos.Add(arquivo);
            await _context.SaveChangesAsync();

            RelArquivoOuvidoria relacionamento = new RelArquivoOuvidoria();
            relacionamento.arqId = arquivo.id;
            relacionamento.ouvNa = na;
            _context.RelArquivosOuvidorias.Add(relacionamento);
            await _context.SaveChangesAsync();

            return Ok(new { id = arquivo.id, nome = arquivo.arq_nome });
        }
    }
}